import type { Pagination } from '@/api/base'
import type { Project, ProjectCreateRequest, ProjectUpdateRequest } from '@/api/project'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { projectApi } from '@/api/project'

export const useProjectStore = defineStore('project', () => {
  // 状态
  const projects = ref<Project[]>([])
  const currentProject = ref<Project | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const pagination = ref<Pagination>({
    current_page: 1,
    page_size: 10,
    total: 0,
  })

  // 计算属性
  const hasProjects = computed(() => projects.value?.length > 0)
  const totalPages = computed(() => Math.ceil(pagination.value.total / pagination.value.page_size))

  // 获取项目列表
  const fetchProjects = async (params?: { page?: number, size?: number }) => {
    loading.value = true
    error.value = null
    try {
      const response = await projectApi.getProjects(params)
      projects.value = response.data || []
      pagination.value = response.pagination
    }
    catch (err: any) {
      error.value = err.response?.message || '获取项目列表失败'
      console.error('获取项目列表失败:', err)
    }
    finally {
      loading.value = false
    }
  }

  // 创建项目
  const createProject = async (data: ProjectCreateRequest) => {
    loading.value = true
    error.value = null
    try {
      const response = await projectApi.createProject(data)
      projects.value.unshift(response.data)
      pagination.value.total += 1
      return response.data
    }
    catch (err: any) {
      error.value = err.response?.message || '创建项目失败'
      console.error('创建项目失败:', err)
      throw err
    }
    finally {
      loading.value = false
    }
  }

  // 获取项目详情
  const fetchProject = async (id: string) => {
    loading.value = true
    error.value = null
    try {
      const response = await projectApi.getProject(id)
      currentProject.value = response.data
      return response.data
    }
    catch (err: any) {
      error.value = err.response?.message || '获取项目详情失败'
      console.error('获取项目详情失败:', err)
      throw err
    }
    finally {
      loading.value = false
    }
  }

  // 更新项目
  const updateProject = async (id: string, data: ProjectUpdateRequest) => {
    loading.value = true
    error.value = null
    try {
      const response = await projectApi.updateProject(id, data)
      const index = projects.value.findIndex(p => p.id === id)
      if (index !== -1) {
        projects.value[index] = response.data
      }
      if (currentProject.value?.id === id) {
        currentProject.value = response.data
      }
      return response.data
    }
    catch (err: any) {
      error.value = err.response?.message || '更新项目失败'
      console.error('更新项目失败:', err)
      throw err
    }
    finally {
      loading.value = false
    }
  }

  // 删除项目
  const deleteProject = async (id: string) => {
    loading.value = true
    error.value = null
    try {
      await projectApi.deleteProject(id)
      projects.value = projects.value.filter(p => p.id !== id)
      pagination.value.total -= 1
      if (currentProject.value?.id === id) {
        currentProject.value = null
      }
    }
    catch (err: any) {
      error.value = err.response?.message || '删除项目失败'
      console.error('删除项目失败:', err)
      throw err
    }
    finally {
      loading.value = false
    }
  }

  // 设置当前项目
  const setCurrentProject = (project: Project | null) => {
    currentProject.value = project
  }

  // 根据ID设置当前项目
  const setCurrentProjectById = async (projectId: string) => {
    // 先从已加载的项目列表中查找
    const existingProject = projects.value.find(p => p.id === projectId)
    if (existingProject) {
      setCurrentProject(existingProject)
      return existingProject
    }

    // 如果没有找到，则从API获取
    try {
      const project = await fetchProject(projectId)
      return project
    }
    catch (err) {
      console.error('设置当前项目失败:', err)
      throw err
    }
  }

  // 初始化项目选择（从持久化存储中恢复或选择第一个项目）
  const initializeProject = async () => {
    // 如果已经有当前项目，则不需要初始化
    if (currentProject.value) {
      return currentProject.value
    }

    // 确保项目列表已加载
    if (projects.value.length === 0) {
      await fetchProjects()
    }

    // 如果仍然没有项目，则返回null
    if (projects.value.length === 0) {
      return null
    }

    // 选择第一个项目作为默认项目
    const firstProject = projects.value[0]
    setCurrentProject(firstProject)
    return firstProject
  }

  // 清空状态
  const clearState = () => {
    projects.value = []
    currentProject.value = null
    error.value = null
    pagination.value = {
      current_page: 1,
      page_size: 10,
      total: 0,
    }
  }

  return {
    // 状态
    projects,
    currentProject,
    loading,
    error,
    pagination,

    // 计算属性
    hasProjects,
    totalPages,

    // 方法
    fetchProjects,
    createProject,
    fetchProject,
    updateProject,
    deleteProject,
    setCurrentProject,
    setCurrentProjectById,
    initializeProject,
    clearState,
  }
}, {
  persist: true,
})
