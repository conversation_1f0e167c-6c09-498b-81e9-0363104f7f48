<script setup lang="ts">
import type { MonitorEvent } from '@/api/monitor'
import { <PERSON>Lef<PERSON>, Filter, RefreshCw } from 'lucide-vue-next'
import { computed, h, onMounted, reactive, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { toast } from 'vue-sonner'
import DataCard from '@/components/monitor/DataCard.vue'
// 导入新组件
import DataTable from '@/components/monitor/DataTable.vue'
import ErrorDetailDialog from '@/components/monitor/ErrorDetailDialog.vue'
import ResponsiveContainer from '@/components/monitor/ResponsiveContainer.vue'
import ResponsiveGrid from '@/components/monitor/ResponsiveGrid.vue'
import TimeRangeSelector from '@/components/monitor/TimeRangeSelector.vue'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'

import { Label } from '@/components/ui/label'
import { Pagination } from '@/components/ui/pagination'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useMonitorStore } from '@/store/monitor'
import { useProjectStore } from '@/store/project'
import {
  formatTimestamp,
  formatUrl,
  getErrorMessage,
  getErrorSeverity,
  getErrorSeverityInfo,
  getErrorTypeName,
} from '@/utils/eventDataMapper'

const router = useRouter()
const route = useRoute()
const projectStore = useProjectStore()
const monitorStore = useMonitorStore()

// 状态
const loading = ref(false)
const showErrorDetail = ref(false)
const selectedError = ref<MonitorEvent | null>(null)
const currentPage = ref(1)

// 筛选条件
const filters = reactive({
  timeRange: '24h',
  eventId: '',
  userUuid: '',
  pageUrl: '',
  customStartTime: '',
  customEndTime: '',
})

// 计算属性
const currentProject = computed(() => projectStore.currentProject)
const errors = computed(() => monitorStore.errors)
const pagination = computed(() => monitorStore.pagination)
const totalPages = computed(() => monitorStore.totalPages)

// 统计信息
const errorStats = computed(() => {
  const uniqueUsers = new Set(errors.value.map(e => e.user_uuid)).size
  const uniquePages = new Set(errors.value.map(e => e.trigger_page_url)).size
  const totalErrors = pagination.value.total

  // 按错误类型分组
  const errorsByType = errors.value.reduce((acc, error) => {
    const type = error.event_id
    acc[type] = (acc[type] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  // 按严重程度分组
  const errorsBySeverity = errors.value.reduce((acc, error) => {
    const severity = getErrorSeverity(error.event_data)
    acc[severity] = (acc[severity] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  return {
    totalErrors,
    uniqueUsers,
    uniquePages,
    errorsByType,
    errorsBySeverity,
  }
})

// 表格列配置
const tableColumns = computed(() => [
  {
    key: 'event_id',
    title: '错误类型',
    sortable: true,
    render: (value: string) => {
      const severity = getErrorSeverity({ eventId: value })
      const severityInfo = getErrorSeverityInfo(severity)
      return h('div', { class: 'flex items-center gap-2' }, [
        h(Badge, {
          variant: 'outline',
          class: [severityInfo.color, severityInfo.bgColor],
        }, () => getErrorTypeName(value)),
      ])
    },
  },
  {
    key: 'event_data',
    title: '错误信息',
    render: (value: any) => {
      const message = getErrorMessage(value)
      return h('div', {
        class: 'max-w-xs truncate',
        title: message,
      }, message)
    },
  },
  {
    key: 'trigger_page_url',
    title: '页面',
    render: (value: string) => {
      const urlInfo = formatUrl(value, 30)
      return h('div', {
        class: 'max-w-xs truncate',
        title: urlInfo.full,
      }, urlInfo.display)
    },
  },
  {
    key: 'user_uuid',
    title: '用户',
    render: (value: string) => {
      return h('code', { class: 'text-xs' }, `${value?.slice(0, 8)}...` || '未知')
    },
  },
  {
    key: 'trigger_time',
    title: '发生时间',
    sortable: true,
    render: (value: number) => {
      return formatTimestamp(value, 'relative')
    },
  },
])

// 表格行操作
const tableActions = [
  {
    label: '查看详情',
    action: (record: MonitorEvent) => {
      selectedError.value = record
      showErrorDetail.value = true
    },
  },
]

// 时间范围变化处理
function handleTimeRangeChange(timeRangeData: { startTime: number, endTime: number }) {
  currentPage.value = 1
  loadData(timeRangeData.startTime, timeRangeData.endTime)
}

// 加载数据
async function loadData(startTime?: number, endTime?: number) {
  if (!currentProject.value)
    return

  loading.value = true
  try {
    // 如果没有传入时间参数，使用默认的24小时
    const now = Date.now()
    const defaultStartTime = startTime || (now - 24 * 60 * 60 * 1000)
    const defaultEndTime = endTime || now

    const params: any = {
      project_id: currentProject.value.id,
      page: currentPage.value,
      page_size: 20,
      start_time: defaultStartTime,
      end_time: defaultEndTime,
    }

    if (filters.eventId)
      params.event_id = filters.eventId
    if (filters.userUuid)
      params.user_uuid = filters.userUuid
    if (filters.pageUrl)
      params.page_url = filters.pageUrl

    await monitorStore.fetchErrors(params)
  }
  catch (error) {
    console.error('加载错误数据失败:', error)
    toast.error('加载错误数据失败')
  }
  finally {
    loading.value = false
  }
}

// 刷新数据
function refreshData() {
  currentPage.value = 1
  loadData()
}

// 重置筛选条件
function resetFilters() {
  filters.timeRange = '24h'
  filters.eventId = ''
  filters.userUuid = ''
  filters.pageUrl = ''
  filters.customStartTime = ''
  filters.customEndTime = ''
  currentPage.value = 1
  loadData()
}

// 分页处理
function handlePageChange(page: number) {
  currentPage.value = page
  loadData()
}

// 表格行点击处理
function handleRowClick(record: MonitorEvent) {
  selectedError.value = record
  showErrorDetail.value = true
}

// 筛选条件变化处理
function handleFilterChange() {
  currentPage.value = 1
  loadData()
}

// 初始化项目
async function initProject() {
  const projectId = route.params.id as string
  if (projectId && (!currentProject.value || currentProject.value.id !== projectId)) {
    try {
      await projectStore.fetchProject(projectId)
    }
    catch (error) {
      console.error('获取项目信息失败:', error)
      toast.error('获取项目信息失败')
      router.push('/projects')
      return
    }
  }

  loadData()
}

// 监听路由变化
watch(() => route.params.id, initProject, { immediate: true })

// 生命周期
onMounted(() => {
  // 设置默认的自定义时间（最近24小时）
  const now = new Date()
  const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)

  filters.customEndTime = now.toISOString().slice(0, 16)
  filters.customStartTime = yesterday.toISOString().slice(0, 16)
})
</script>

<template>
  <ResponsiveContainer type="page" max-width="1400px" centered>
    <!-- 页头 -->
    <div class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4 mb-6">
      <div class="flex items-center gap-4">
        <Button variant="ghost" size="sm" @click="router.back()">
          <ArrowLeft class="w-4 h-4 mr-2" />
          返回
        </Button>
        <div>
          <h1 class="text-2xl lg:text-3xl font-bold">
            错误监控
          </h1>
          <p v-if="currentProject" class="text-muted-foreground mt-1">
            项目: {{ currentProject.name }}
          </p>
        </div>
      </div>
      <div class="flex items-center gap-4">
        <Button :disabled="loading" size="sm" @click="refreshData">
          <RefreshCw :class="{ 'animate-spin': loading }" class="w-4 h-4 mr-2" />
          刷新
        </Button>
      </div>
    </div>

    <!-- 时间范围和筛选条件 -->
    <Card class="mb-6">
      <CardHeader>
        <div class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
          <CardTitle class="text-lg flex items-center gap-2">
            <Filter class="w-5 h-5" />
            筛选条件
          </CardTitle>
          <TimeRangeSelector
            v-model="filters.timeRange"
            v-model:custom-start-time="filters.customStartTime"
            v-model:custom-end-time="filters.customEndTime"
            :disabled="loading"
            @change="handleTimeRangeChange"
          />
        </div>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="space-y-2">
            <Label>错误类型</Label>
            <Select v-model="filters.eventId" @update:model-value="handleFilterChange">
              <SelectTrigger>
                <SelectValue placeholder="选择错误类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">
                  全部类型
                </SelectItem>
                <SelectItem value="script.error">
                  脚本错误
                </SelectItem>
                <SelectItem value="resource.error">
                  资源错误
                </SelectItem>
                <SelectItem value="promise.error">
                  Promise错误
                </SelectItem>
                <SelectItem value="vue.error">
                  Vue错误
                </SelectItem>
                <SelectItem value="console.error">
                  控制台错误
                </SelectItem>
                <SelectItem value="network.error">
                  网络错误
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div class="space-y-2">
            <Label>用户ID</Label>
            <Input
              v-model="filters.userUuid"
              placeholder="用户UUID"
              @keyup.enter="handleFilterChange"
            />
          </div>
          <div class="space-y-2">
            <Label>页面URL</Label>
            <Input
              v-model="filters.pageUrl"
              placeholder="页面地址"
              @keyup.enter="handleFilterChange"
            />
          </div>
        </div>
        <div class="flex justify-end mt-4">
          <Button variant="outline" @click="resetFilters">
            <RefreshCw class="w-4 h-4 mr-2" />
            重置筛选
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- 错误统计卡片 -->
    <ResponsiveGrid
      :cols="{ xs: 1, sm: 2, md: 2, lg: 4 }"
      gap="md"
      class="mb-6"
    >
      <DataCard
        title="总错误数"
        description="当前时间范围内的错误总数"
        :value="errorStats.totalErrors"
        status="error"
        :loading="loading"
      />

      <DataCard
        title="影响用户数"
        description="发生错误的唯一用户数"
        :value="errorStats.uniqueUsers"
        status="warning"
        :loading="loading"
      />

      <DataCard
        title="错误页面数"
        description="发生错误的页面数量"
        :value="errorStats.uniquePages"
        status="info"
        :loading="loading"
      />

      <DataCard
        title="严重错误"
        description="高危和严重级别错误"
        :value="(errorStats.errorsBySeverity.critical || 0) + (errorStats.errorsBySeverity.high || 0)"
        status="error"
        :loading="loading"
      />
    </ResponsiveGrid>

    <!-- 错误列表 -->
    <Card>
      <CardHeader>
        <div class="flex justify-between items-center">
          <CardTitle>错误事件列表</CardTitle>
          <div class="flex items-center gap-2">
            <span class="text-sm text-muted-foreground">
              共 {{ pagination.total }} 条记录
            </span>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <DataTable
          :data="errors"
          :columns="tableColumns"
          :loading="loading"
          :row-actions="tableActions"
          searchable
          search-placeholder="搜索错误信息、页面URL..."
          empty-text="暂无错误事件"
          @row-click="handleRowClick"
        />
      </CardContent>
    </Card>

    <!-- 分页 -->
    <div v-if="errors.length > 0 && totalPages > 1" class="mt-6 flex justify-center">
      <Pagination
        v-model:page="currentPage"
        :total="pagination.total"
        :items-per-page="pagination.page_size"
        :sibling-count="1"
        show-edges
        @update:page="handlePageChange"
      />
    </div>

    <!-- 错误详情对话框 -->
    <ErrorDetailDialog
      v-model:open="showErrorDetail"
      :error="selectedError"
    />
  </ResponsiveContainer>
</template>

<style scoped>
.error-monitor {
  padding: 1.5rem;
}

@media (min-width: 768px) {
  .error-monitor {
    padding: 2rem;
  }
}
</style>
