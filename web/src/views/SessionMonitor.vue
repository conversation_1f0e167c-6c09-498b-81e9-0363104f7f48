<script setup lang="ts">
import type { UserSession } from '@/api/monitor'
import { Filter, Users } from 'lucide-vue-next'
import { computed, h, onMounted, reactive, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { toast } from 'vue-sonner'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useMonitorStore } from '@/store/monitor'
import { useProjectStore } from '@/store/project'
import {
  formatTimestamp,
  formatDuration,
} from '@/utils/eventDataMapper'

// 导入新组件
import DataTable from '@/components/monitor/DataTable.vue'
import TimeRangeSelector from '@/components/monitor/TimeRangeSelector.vue'
import SessionDetailDialog from '@/components/monitor/SessionDetailDialog.vue'
import DataCard from '@/components/monitor/DataCard.vue'
import MonitorHeader from '@/components/monitor/MonitorHeader.vue'
import ResponsiveContainer from '@/components/monitor/ResponsiveContainer.vue'
import ResponsiveGrid from '@/components/monitor/ResponsiveGrid.vue'

const router = useRouter()
const route = useRoute()
const projectStore = useProjectStore()
const monitorStore = useMonitorStore()

// 状态
const loading = ref(false)
const showSessionDetail = ref(false)
const selectedSession = ref<UserSession | null>(null)
const currentPage = ref(1)

// 筛选条件
const filters = reactive({
  timeRange: '24h',
  userUuid: '',
  platform: '',
  customStartTime: '',
  customEndTime: '',
})

// 计算属性
const currentProject = computed(() => projectStore.currentProject)
const sessions = computed(() => monitorStore.sessions)
const pagination = computed(() => monitorStore.pagination)
const totalPages = computed(() => monitorStore.totalPages)

// 会话统计
const sessionStats = computed(() => {
  const data = sessions.value

  if (data.length === 0) {
    return {
      totalSessions: 0,
      uniqueUsers: 0,
      averageDuration: 0,
      activeSessions: 0,
      averageEvents: 0,
      errorSessions: 0,
    }
  }

  const totalSessions = pagination.value.total
  const uniqueUsers = new Set(data.map(session => session.user_uuid)).size
  const totalDuration = data.reduce((sum, session) => sum + (session.duration || 0), 0)
  const averageDuration = Math.round(totalDuration / data.length)

  // 活跃会话（最近5分钟有活动）
  const now = Date.now()
  const fiveMinutesAgo = now - 5 * 60 * 1000
  const activeSessions = data.filter(session => session.last_visit > fiveMinutesAgo).length

  // 平均事件数
  const totalEvents = data.reduce((sum, session) => sum + (session.event_count || 0), 0)
  const averageEvents = Math.round(totalEvents / data.length)

  // 有错误的会话数
  const errorSessions = data.filter(session => (session.errors || 0) > 0).length

  return {
    totalSessions,
    uniqueUsers,
    averageDuration,
    activeSessions,
    averageEvents,
    errorSessions,
  }
})

// 表格列配置
const tableColumns = computed(() => [
  {
    key: 'user_uuid',
    title: '用户',
    render: (value: string) => {
      return h('code', { class: 'text-xs' }, value?.slice(0, 8) + '...' || '未知')
    },
  },
  {
    key: 'session_id',
    title: '会话ID',
    render: (value: string) => {
      return h('code', { class: 'text-xs' }, value?.slice(0, 12) + '...' || '未知')
    },
  },
  {
    key: 'platform',
    title: '平台',
    render: (value: string) => {
      return h(Badge, { variant: 'outline' }, () => value || '未知')
    },
  },
  {
    key: 'duration',
    title: '会话时长',
    sortable: true,
    render: (value: number) => {
      return formatDuration(value || 0)
    },
  },
  {
    key: 'event_count',
    title: '事件数',
    sortable: true,
    render: (value: number) => {
      return h('span', { class: 'font-medium' }, value || 0)
    },
  },
  {
    key: 'errors',
    title: '错误数',
    render: (value: number) => {
      const errorCount = value || 0
      return h(Badge, {
        variant: errorCount > 0 ? 'destructive' : 'secondary'
      }, () => errorCount)
    },
  },
  {
    key: 'last_visit',
    title: '最后活动',
    sortable: true,
    render: (value: number) => {
      return formatTimestamp(value, 'relative')
    },
  },
])

// 表格行操作
const tableActions = [
  {
    label: '查看详情',
    action: (record: UserSession) => {
      selectedSession.value = record
      showSessionDetail.value = true
    },
  },
  {
    label: '查看事件',
    action: (record: UserSession) => {
      router.push(`/monitor/${currentProject.value?.id}/events?session=${record.session_id}`)
    },
  },
]

// 时间范围变化处理
function handleTimeRangeChange(timeRangeData: { startTime: number; endTime: number }) {
  currentPage.value = 1
  loadData(timeRangeData.startTime, timeRangeData.endTime)
}

// 加载数据
async function loadData(startTime?: number, endTime?: number) {
  if (!currentProject.value) return

  loading.value = true
  try {
    // 如果没有传入时间参数，使用默认的24小时
    const now = Date.now()
    const defaultStartTime = startTime || (now - 24 * 60 * 60 * 1000)
    const defaultEndTime = endTime || now

    const params: any = {
      project_id: currentProject.value.id,
      page: currentPage.value,
      page_size: 20,
      start_time: defaultStartTime,
      end_time: defaultEndTime,
    }

    if (filters.userUuid) params.user_uuid = filters.userUuid
    if (filters.platform) params.platform = filters.platform

    await monitorStore.fetchSessions(params)
  }
  catch (error) {
    console.error('加载会话数据失败:', error)
    toast.error('加载会话数据失败')
  }
  finally {
    loading.value = false
  }
}

// 刷新数据
function refreshData() {
  currentPage.value = 1
  loadData()
}

// 重置筛选条件
function resetFilters() {
  filters.timeRange = '24h'
  filters.userUuid = ''
  filters.platform = ''
  filters.customStartTime = ''
  filters.customEndTime = ''
  currentPage.value = 1
  loadData()
}

// 筛选条件变化处理
function handleFilterChange() {
  currentPage.value = 1
  loadData()
}

// 分页处理
function handlePageChange(page: number) {
  currentPage.value = page
  loadData()
}

// 表格行点击处理
function handleRowClick(record: UserSession) {
  selectedSession.value = record
  showSessionDetail.value = true
}

// 查看会话事件
function handleViewEvents(sessionId: string) {
  router.push(`/monitor/${currentProject.value?.id}/events?session=${sessionId}`)
}

// 初始化项目
async function initProject() {
  const projectId = route.params.id as string
  if (projectId && (!currentProject.value || currentProject.value.id !== projectId)) {
    try {
      await projectStore.fetchProject(projectId)
    }
    catch (error) {
      console.error('获取项目信息失败:', error)
      toast.error('获取项目信息失败')
      router.push('/projects')
      return
    }
  }

  loadData()
}

// 监听路由变化
watch(() => route.params.id, initProject, { immediate: true })

// 监听项目切换
watch(() => projectStore.currentProject, (newProject, oldProject) => {
  // 只有当项目真正发生变化时才刷新数据
  if (newProject && oldProject && newProject.id !== oldProject.id) {
    loadData()
  }
}, { deep: true })

// 生命周期
onMounted(() => {
  // 设置默认的自定义时间（最近24小时）
  const now = new Date()
  const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)

  filters.customEndTime = now.toISOString().slice(0, 16)
  filters.customStartTime = yesterday.toISOString().slice(0, 16)
})
</script>

<template>
  <ResponsiveContainer type="page" max-width="1400px" centered>
    <!-- 页头 -->
    <MonitorHeader
      title="用户会话监控"
      description="监控和分析用户会话数据"
      :loading="loading"
      :on-refresh="refreshData"
    />

    <!-- 时间范围和筛选条件 -->
    <Card class="mb-6">
      <CardHeader>
        <div class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
          <CardTitle class="text-lg flex items-center gap-2">
            <Filter class="w-5 h-5" />
            筛选条件
          </CardTitle>
          <TimeRangeSelector
            v-model="filters.timeRange"
            v-model:custom-start-time="filters.customStartTime"
            v-model:custom-end-time="filters.customEndTime"
            :disabled="loading"
            @change="handleTimeRangeChange"
          />
        </div>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-2">
            <Label>用户ID</Label>
            <Input
              v-model="filters.userUuid"
              placeholder="用户UUID"
              @keyup.enter="handleFilterChange"
            />
          </div>
          <div class="space-y-2">
            <Label>平台</Label>
            <Select v-model="filters.platform" @update:model-value="handleFilterChange">
              <SelectTrigger>
                <SelectValue placeholder="选择平台" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">全部平台</SelectItem>
                <SelectItem value="web">Web</SelectItem>
                <SelectItem value="mobile">Mobile</SelectItem>
                <SelectItem value="desktop">Desktop</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <div class="flex justify-end mt-4">
          <Button variant="outline" @click="resetFilters">
            <RefreshCw class="w-4 h-4 mr-2" />
            重置筛选
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- 会话统计卡片 -->
    <ResponsiveGrid
      :cols="{ xs: 1, sm: 2, md: 2, lg: 4 }"
      gap="md"
      class="mb-6"
    >
      <DataCard
        title="总会话数"
        description="用户会话总数"
        :value="sessionStats.totalSessions"
        status="info"
        :loading="loading"
      />

      <DataCard
        title="平均时长"
        description="会话平均持续时间"
        :value="formatDuration(sessionStats.averageDuration)"
        status="success"
        :loading="loading"
      />

      <DataCard
        title="独立用户"
        description="唯一访问用户数"
        :value="sessionStats.uniqueUsers"
        status="warning"
        :loading="loading"
      />

      <DataCard
        title="活跃会话"
        description="最近5分钟活跃"
        :value="sessionStats.activeSessions"
        :status="sessionStats.activeSessions > 0 ? 'success' : 'info'"
        :loading="loading"
      />
    </ResponsiveGrid>

    <!-- 会话数据表格 -->
    <Card>
      <CardHeader>
        <div class="flex justify-between items-center">
          <CardTitle>用户会话</CardTitle>
          <div class="flex items-center gap-2">
            <span class="text-sm text-muted-foreground">
              共 {{ pagination.total }} 条记录
            </span>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <DataTable
          :data="sessions"
          :columns="tableColumns"
          :loading="loading"
          :row-actions="tableActions"
          searchable
          search-placeholder="搜索用户ID、会话ID..."
          empty-text="暂无会话数据"
          @row-click="handleRowClick"
        />
      </CardContent>
    </Card>

    <!-- 分页 -->
    <div v-if="totalPages > 1" class="flex justify-center mt-6">
      <Pagination
        :current-page="currentPage"
        :total-pages="totalPages"
        @page-change="handlePageChange"
      />
    </div>

    <!-- 会话详情对话框 -->
    <SessionDetailDialog
      v-model:open="showSessionDetail"
      :session="selectedSession"
    />
  </ResponsiveContainer>
</template>

<style scoped>
.session-monitor {
  padding: 1.5rem;
}

@media (min-width: 768px) {
  .session-monitor {
    padding: 2rem;
  }
}
</style>
