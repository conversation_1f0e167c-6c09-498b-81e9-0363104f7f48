<script setup lang="ts">
import type { MonitorEvent } from '@/api/monitor'
import { <PERSON><PERSON><PERSON><PERSON>, Refresh<PERSON>w, Filter, Zap } from 'lucide-vue-next'
import { computed, h, onMounted, reactive, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { toast } from 'vue-sonner'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useMonitorStore } from '@/store/monitor'
import { useProjectStore } from '@/store/project'
import {
  formatTimestamp,
  formatUrl,
  getDeviceInfo,
  getPerformanceScore,
  formatPerformanceMetric,
  formatDuration,
} from '@/utils/eventDataMapper'

// 导入新组件
import DataTable from '@/components/monitor/DataTable.vue'
import TimeRangeSelector from '@/components/monitor/TimeRangeSelector.vue'
import PerformanceDetailDialog from '@/components/monitor/PerformanceDetailDialog.vue'
import DataCard from '@/components/monitor/DataCard.vue'
import ResponsiveContainer from '@/components/monitor/ResponsiveContainer.vue'
import ResponsiveGrid from '@/components/monitor/ResponsiveGrid.vue'
import PerformanceMetrics from '@/components/monitor/PerformanceMetrics.vue'

const router = useRouter()
const route = useRoute()
const projectStore = useProjectStore()
const monitorStore = useMonitorStore()

// 状态
const loading = ref(false)
const showPerformanceDetail = ref(false)
const selectedPerformance = ref<MonitorEvent | null>(null)
const currentPage = ref(1)

// 筛选条件
const filters = reactive({
  timeRange: '24h',
  metricType: '',
  pageUrl: '',
  userUuid: '',
  customStartTime: '',
  customEndTime: '',
})

// 计算属性
const currentProject = computed(() => projectStore.currentProject)
const performanceData = computed(() => monitorStore.performance)
const pagination = computed(() => monitorStore.pagination)
const totalPages = computed(() => monitorStore.totalPages)

// 性能统计信息
const performanceStats = computed(() => {
  const data = performanceData.value

  // 计算各项指标的平均值
  const lcpValues = data.filter(item => item.event_data?.lcp).map(item => item.event_data.lcp)
  const fcpValues = data.filter(item => item.event_data?.fcp).map(item => item.event_data.fcp)
  const fidValues = data.filter(item => item.event_data?.fid).map(item => item.event_data.fid)
  const clsValues = data.filter(item => item.event_data?.cls).map(item => item.event_data.cls)

  const averageLCP = lcpValues.length > 0 ? Math.round(lcpValues.reduce((a, b) => a + b, 0) / lcpValues.length) : 0
  const averageFCP = fcpValues.length > 0 ? Math.round(fcpValues.reduce((a, b) => a + b, 0) / fcpValues.length) : 0
  const averageFID = fidValues.length > 0 ? Math.round(fidValues.reduce((a, b) => a + b, 0) / fidValues.length) : 0
  const averageCLS = clsValues.length > 0 ? (clsValues.reduce((a, b) => a + b, 0) / clsValues.length) : 0

  // 计算综合性能评分
  const mockEventData = {
    load: averageLCP,
    fcp: averageFCP,
    lcp: averageLCP,
    fid: averageFID,
    cls: averageCLS,
  }
  const score = getPerformanceScore(mockEventData)

  // 统计页面数量
  const uniquePages = new Set(data.map(item => item.trigger_page_url)).size
  const uniqueUsers = new Set(data.map(item => item.user_uuid)).size

  return {
    totalRecords: pagination.value.total,
    uniquePages,
    uniqueUsers,
    averageLCP,
    averageFCP,
    averageFID,
    averageCLS,
    score,
  }
})

// 表格列配置
const tableColumns = computed(() => [
  {
    key: 'event_id',
    title: '性能指标',
    sortable: true,
    render: (value: string) => {
      const metricMap: Record<string, string> = {
        'paint': '首次绘制',
        'fcp': '首次内容绘制',
        'lcp': '最大内容绘制',
        'fid': '首次输入延迟',
        'cls': '累积布局偏移',
        'navigation': '导航计时',
      }
      return h(Badge, { variant: 'outline' }, () => metricMap[value] || value)
    },
  },
  {
    key: 'trigger_page_url',
    title: '页面',
    render: (value: string) => {
      const urlInfo = formatUrl(value, 40)
      return h('div', {
        class: 'max-w-xs truncate',
        title: urlInfo.full
      }, urlInfo.display)
    },
  },
  {
    key: 'event_data',
    title: '性能数据',
    render: (value: any) => {
      const metrics = []
      if (value.lcp) metrics.push(`LCP: ${formatDuration(value.lcp)}`)
      if (value.fcp) metrics.push(`FCP: ${formatDuration(value.fcp)}`)
      if (value.fid) metrics.push(`FID: ${formatDuration(value.fid)}`)
      if (value.cls) metrics.push(`CLS: ${value.cls.toFixed(3)}`)

      return h('div', { class: 'text-xs space-y-1' },
        metrics.map(metric => h('div', metric))
      )
    },
  },
  {
    key: 'user_uuid',
    title: '用户',
    render: (value: string) => {
      return h('code', { class: 'text-xs' }, value?.slice(0, 8) + '...' || '未知')
    },
  },
  {
    key: 'trigger_time',
    title: '记录时间',
    sortable: true,
    render: (value: number) => {
      return formatTimestamp(value, 'relative')
    },
  },
])

// 表格行操作
const tableActions = [
  {
    label: '查看详情',
    action: (record: MonitorEvent) => {
      selectedPerformance.value = record
      showPerformanceDetail.value = true
    },
  },
]

// 时间范围变化处理
function handleTimeRangeChange(timeRangeData: { startTime: number; endTime: number }) {
  currentPage.value = 1
  loadData(timeRangeData.startTime, timeRangeData.endTime)
}

// 加载数据
async function loadData(startTime?: number, endTime?: number) {
  if (!currentProject.value) return

  loading.value = true
  try {
    // 如果没有传入时间参数，使用默认的24小时
    const now = Date.now()
    const defaultStartTime = startTime || (now - 24 * 60 * 60 * 1000)
    const defaultEndTime = endTime || now

    const params: any = {
      project_id: currentProject.value.id,
      page: currentPage.value,
      page_size: 20,
      start_time: defaultStartTime,
      end_time: defaultEndTime,
    }

    if (filters.metricType) params.event_id = filters.metricType
    if (filters.pageUrl) params.page_url = filters.pageUrl
    if (filters.userUuid) params.user_uuid = filters.userUuid

    await monitorStore.fetchPerformance(params)
  }
  catch (error) {
    console.error('加载性能数据失败:', error)
    toast.error('加载性能数据失败')
  }
  finally {
    loading.value = false
  }
}

// 刷新数据
function refreshData() {
  currentPage.value = 1
  loadData()
}

// 重置筛选条件
function resetFilters() {
  filters.timeRange = '24h'
  filters.metricType = ''
  filters.pageUrl = ''
  filters.userUuid = ''
  filters.customStartTime = ''
  filters.customEndTime = ''
  currentPage.value = 1
  loadData()
}

// 筛选条件变化处理
function handleFilterChange() {
  currentPage.value = 1
  loadData()
}

// 分页处理
function handlePageChange(page: number) {
  currentPage.value = page
  loadData()
}

// 表格行点击处理
function handleRowClick(record: MonitorEvent) {
  selectedPerformance.value = record
  showPerformanceDetail.value = true
}

// 初始化项目
async function initProject() {
  const projectId = route.params.id as string
  if (projectId && (!currentProject.value || currentProject.value.id !== projectId)) {
    try {
      await projectStore.fetchProject(projectId)
    }
    catch (error) {
      console.error('获取项目信息失败:', error)
      toast.error('获取项目信息失败')
      router.push('/projects')
      return
    }
  }

  loadData()
}

// 监听路由变化
watch(() => route.params.id, initProject, { immediate: true })

// 生命周期
onMounted(() => {
  // 设置默认的自定义时间（最近24小时）
  const now = new Date()
  const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)

  filters.customEndTime = now.toISOString().slice(0, 16)
  filters.customStartTime = yesterday.toISOString().slice(0, 16)
})
</script>

<template>
  <ResponsiveContainer type="page" max-width="1400px" centered>
    <!-- 页头 -->
    <div class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4 mb-6">
      <div class="flex items-center gap-4">
        <Button variant="ghost" size="sm" @click="router.back()">
          <ArrowLeft class="w-4 h-4 mr-2" />
          返回
        </Button>
        <div>
          <h1 class="text-2xl lg:text-3xl font-bold">性能监控</h1>
          <p v-if="currentProject" class="text-muted-foreground mt-1">
            项目: {{ currentProject.name }}
          </p>
        </div>
      </div>
      <div class="flex items-center gap-4">
        <Button :disabled="loading" size="sm" @click="refreshData">
          <RefreshCw :class="{ 'animate-spin': loading }" class="w-4 h-4 mr-2" />
          刷新
        </Button>
      </div>
    </div>

    <!-- 时间范围和筛选条件 -->
    <Card class="mb-6">
      <CardHeader>
        <div class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
          <CardTitle class="text-lg flex items-center gap-2">
            <Filter class="w-5 h-5" />
            筛选条件
          </CardTitle>
          <TimeRangeSelector
            v-model="filters.timeRange"
            v-model:custom-start-time="filters.customStartTime"
            v-model:custom-end-time="filters.customEndTime"
            :disabled="loading"
            @change="handleTimeRangeChange"
          />
        </div>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="space-y-2">
            <Label>性能指标</Label>
            <Select v-model="filters.metricType" @update:model-value="handleFilterChange">
              <SelectTrigger>
                <SelectValue placeholder="选择性能指标" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">全部指标</SelectItem>
                <SelectItem value="paint">首次绘制时间</SelectItem>
                <SelectItem value="fcp">首次内容绘制</SelectItem>
                <SelectItem value="lcp">最大内容绘制</SelectItem>
                <SelectItem value="fid">首次输入延迟</SelectItem>
                <SelectItem value="cls">累积布局偏移</SelectItem>
                <SelectItem value="navigation">导航计时</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div class="space-y-2">
            <Label>页面URL</Label>
            <Input
              v-model="filters.pageUrl"
              placeholder="页面地址"
              @keyup.enter="handleFilterChange"
            />
          </div>
          <div class="space-y-2">
            <Label>用户ID</Label>
            <Input
              v-model="filters.userUuid"
              placeholder="用户UUID"
              @keyup.enter="handleFilterChange"
            />
          </div>
        </div>
        <div class="flex justify-end mt-4">
          <Button variant="outline" @click="resetFilters">
            <RefreshCw class="w-4 h-4 mr-2" />
            重置筛选
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- 性能统计卡片 -->
    <ResponsiveGrid
      :cols="{ xs: 1, sm: 2, md: 3, lg: 5 }"
      gap="md"
      class="mb-6"
    >
      <DataCard
        title="平均 LCP"
        description="最大内容绘制时间"
        :value="performanceStats.averageLCP"
        unit="ms"
        :status="performanceStats.averageLCP <= 2500 ? 'success' : performanceStats.averageLCP <= 4000 ? 'warning' : 'error'"
        :loading="loading"
      />

      <DataCard
        title="平均 FCP"
        description="首次内容绘制时间"
        :value="performanceStats.averageFCP"
        unit="ms"
        :status="performanceStats.averageFCP <= 1800 ? 'success' : performanceStats.averageFCP <= 3000 ? 'warning' : 'error'"
        :loading="loading"
      />

      <DataCard
        title="平均 FID"
        description="首次输入延迟"
        :value="performanceStats.averageFID"
        unit="ms"
        :status="performanceStats.averageFID <= 100 ? 'success' : performanceStats.averageFID <= 300 ? 'warning' : 'error'"
        :loading="loading"
      />

      <DataCard
        title="平均 CLS"
        description="累积布局偏移"
        :value="performanceStats.averageCLS.toFixed(3)"
        :status="performanceStats.averageCLS <= 0.1 ? 'success' : performanceStats.averageCLS <= 0.25 ? 'warning' : 'error'"
        :loading="loading"
      />

      <DataCard
        title="性能评分"
        description="综合性能评分"
        :value="performanceStats.score.score"
        :status="performanceStats.score.level === 'excellent' ? 'success' : performanceStats.score.level === 'good' ? 'info' : performanceStats.score.level === 'needs-improvement' ? 'warning' : 'error'"
        :loading="loading"
      />
    </ResponsiveGrid>

    <!-- 性能数据列表 -->
    <Card>
      <CardHeader>
        <div class="flex justify-between items-center">
          <CardTitle>性能数据记录</CardTitle>
          <div class="flex items-center gap-2">
            <span class="text-sm text-muted-foreground">
              共 {{ pagination.total }} 条记录
            </span>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <DataTable
          :data="performanceData"
          :columns="tableColumns"
          :loading="loading"
          :row-actions="tableActions"
          searchable
          search-placeholder="搜索页面URL、用户ID..."
          empty-text="暂无性能数据"
          @row-click="handleRowClick"
        />
      </CardContent>
    </Card>

    <!-- 分页 -->
    <div v-if="totalPages > 1" class="flex justify-center mt-6">
      <Pagination
        :current-page="currentPage"
        :total-pages="totalPages"
        @page-change="handlePageChange"
      />
    </div>

    <!-- 性能详情对话框 -->
    <PerformanceDetailDialog
      v-model:open="showPerformanceDetail"
      :event="selectedPerformance"
    />
  </ResponsiveContainer>
</template>

<style scoped>
.performance-monitor {
  padding: 1.5rem;
}

@media (min-width: 768px) {
  .performance-monitor {
    padding: 2rem;
  }
}
</style>
