<script setup lang="ts">
import { computed } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import {
  formatPerformanceMetric,
  getPerformanceScore,
  formatDuration,
} from '@/utils/eventDataMapper'

interface Props {
  eventData: any
  compact?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  compact: false,
})

const performanceData = computed(() => {
  if (!props.eventData) return null
  
  const score = getPerformanceScore(props.eventData)
  
  // 核心性能指标
  const coreMetrics = [
    { key: 'fcp', label: '首次内容绘制', value: props.eventData.fcp },
    { key: 'lcp', label: '最大内容绘制', value: props.eventData.lcp },
    { key: 'fid', label: '首次输入延迟', value: props.eventData.fid },
    { key: 'cls', label: '累积布局偏移', value: props.eventData.cls },
  ].filter(metric => metric.value !== undefined && metric.value !== null)
  
  // 详细性能指标
  const detailMetrics = [
    { key: 'dns', label: 'DNS解析', value: props.eventData.dns },
    { key: 'tcp', label: 'TCP连接', value: props.eventData.tcp },
    { key: 'ssl', label: 'SSL握手', value: props.eventData.ssllink },
    { key: 'ttfb', label: '首字节时间', value: props.eventData.ttfb },
    { key: 'dom', label: 'DOM解析', value: props.eventData.dom },
    { key: 'load', label: '页面加载', value: props.eventData.loadon || props.eventData.load },
  ].filter(metric => metric.value !== undefined && metric.value !== null)
  
  return {
    score,
    coreMetrics,
    detailMetrics,
  }
})

const scoreColor = computed(() => {
  if (!performanceData.value) return 'text-gray-500'
  
  const { level } = performanceData.value.score
  const colorMap = {
    excellent: 'text-green-600',
    good: 'text-blue-600',
    'needs-improvement': 'text-yellow-600',
    poor: 'text-red-600',
  }
  
  return colorMap[level]
})

const scoreBgColor = computed(() => {
  if (!performanceData.value) return 'bg-gray-100'
  
  const { level } = performanceData.value.score
  const colorMap = {
    excellent: 'bg-green-100',
    good: 'bg-blue-100',
    'needs-improvement': 'bg-yellow-100',
    poor: 'bg-red-100',
  }
  
  return colorMap[level]
})

function getMetricStatus(key: string, value: number): 'good' | 'needs-improvement' | 'poor' {
  // 根据Web Vitals标准判断指标状态
  const thresholds: Record<string, { good: number; poor: number }> = {
    fcp: { good: 1800, poor: 3000 },
    lcp: { good: 2500, poor: 4000 },
    fid: { good: 100, poor: 300 },
    cls: { good: 0.1, poor: 0.25 },
    ttfb: { good: 800, poor: 1800 },
    load: { good: 2000, poor: 4000 },
  }
  
  const threshold = thresholds[key]
  if (!threshold) return 'good'
  
  if (value <= threshold.good) return 'good'
  if (value <= threshold.poor) return 'needs-improvement'
  return 'poor'
}

function getMetricColor(status: 'good' | 'needs-improvement' | 'poor'): string {
  const colorMap = {
    good: 'text-green-600',
    'needs-improvement': 'text-yellow-600',
    poor: 'text-red-600',
  }
  return colorMap[status]
}
</script>

<template>
  <Card v-if="performanceData">
    <CardHeader v-if="!compact">
      <CardTitle class="flex items-center justify-between">
        <span>性能指标</span>
        <Badge :class="[scoreColor, scoreBgColor]">
          {{ performanceData.score.label }} ({{ performanceData.score.score }}分)
        </Badge>
      </CardTitle>
    </CardHeader>
    
    <CardContent class="space-y-4">
      <!-- 性能评分 -->
      <div v-if="compact" class="flex items-center justify-between mb-4">
        <span class="text-sm font-medium">性能评分</span>
        <Badge :class="[scoreColor, scoreBgColor]">
          {{ performanceData.score.label }} ({{ performanceData.score.score }}分)
        </Badge>
      </div>
      
      <div class="space-y-2">
        <div class="flex justify-between text-sm">
          <span>总体评分</span>
          <span :class="scoreColor">{{ performanceData.score.score }}/100</span>
        </div>
        <Progress 
          :value="performanceData.score.score" 
          :class="scoreBgColor"
        />
      </div>
      
      <!-- 核心Web指标 -->
      <div v-if="performanceData.coreMetrics.length > 0">
        <Separator class="my-4" />
        <h4 class="text-sm font-medium mb-3">核心Web指标</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div 
            v-for="metric in performanceData.coreMetrics" 
            :key="metric.key"
            class="flex justify-between items-center p-2 bg-muted rounded"
          >
            <span class="text-sm">{{ metric.label }}</span>
            <span 
              class="text-sm font-medium"
              :class="getMetricColor(getMetricStatus(metric.key, metric.value))"
            >
              {{ formatPerformanceMetric(metric.key, metric.value).value }}{{ formatPerformanceMetric(metric.key, metric.value).unit }}
            </span>
          </div>
        </div>
      </div>
      
      <!-- 详细指标 -->
      <div v-if="performanceData.detailMetrics.length > 0 && !compact">
        <Separator class="my-4" />
        <h4 class="text-sm font-medium mb-3">详细指标</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
          <div 
            v-for="metric in performanceData.detailMetrics" 
            :key="metric.key"
            class="flex justify-between items-center text-sm"
          >
            <span class="text-muted-foreground">{{ metric.label }}</span>
            <span 
              class="font-medium"
              :class="getMetricColor(getMetricStatus(metric.key, metric.value))"
            >
              {{ formatDuration(metric.value) }}
            </span>
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
</template>
