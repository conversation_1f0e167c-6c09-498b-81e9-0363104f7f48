<script setup lang="ts">
import type { MonitorEvent } from '@/api/monitor'
import { computed } from 'vue'
import { Co<PERSON>, ExternalLink, MapPin, Clock, User, Monitor } from 'lucide-vue-next'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { toast } from 'vue-sonner'
import {
  getErrorMessage,
  getErrorTypeName,
  getErrorSeverity,
  getErrorSeverityInfo,
  getErrorStack,
  getErrorFilename,
  getErrorLocation,
  hasErrorLocation,
  hasErrorStack,
  formatTimestamp,
  formatUrl,
  getDeviceInfo,
} from '@/utils/eventDataMapper'

interface Props {
  open: boolean
  error: MonitorEvent | null
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:open': [value: boolean]
}>()

const errorInfo = computed(() => {
  if (!props.error) return null
  
  const severity = getErrorSeverity(props.error.event_data)
  const severityInfo = getErrorSeverityInfo(severity)
  const deviceInfo = getDeviceInfo(props.error.event_data)
  const urlInfo = formatUrl(props.error.trigger_page_url)
  
  return {
    message: getErrorMessage(props.error.event_data),
    typeName: getErrorTypeName(props.error.event_id),
    severity,
    severityInfo,
    stack: getErrorStack(props.error.event_data),
    filename: getErrorFilename(props.error.event_data),
    location: getErrorLocation(props.error.event_data),
    hasLocation: hasErrorLocation(props.error.event_data),
    hasStack: hasErrorStack(props.error.event_data),
    deviceInfo,
    urlInfo,
    timestamp: formatTimestamp(props.error.trigger_time || 0),
  }
})

function copyToClipboard(text: string, label: string) {
  navigator.clipboard.writeText(text).then(() => {
    toast.success(`${label}已复制到剪贴板`)
  }).catch(() => {
    toast.error('复制失败')
  })
}

function openUrl(url: string) {
  window.open(url, '_blank')
}
</script>

<template>
  <Dialog :open="open" @update:open="emit('update:open', $event)">
    <DialogContent class="max-w-4xl max-h-[90vh]">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <span>错误详情</span>
          <Badge 
            v-if="errorInfo" 
            :class="[errorInfo.severityInfo.color, errorInfo.severityInfo.bgColor]"
          >
            {{ errorInfo.severityInfo.label }}
          </Badge>
        </DialogTitle>
      </DialogHeader>
      
      <ScrollArea v-if="errorInfo" class="max-h-[70vh]">
        <div class="space-y-6">
          <!-- 基本信息 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <div class="flex items-center gap-2 text-sm font-medium">
                <MapPin class="w-4 h-4" />
                错误类型
              </div>
              <div class="text-sm">{{ errorInfo.typeName }}</div>
            </div>
            
            <div class="space-y-2">
              <div class="flex items-center gap-2 text-sm font-medium">
                <Clock class="w-4 h-4" />
                发生时间
              </div>
              <div class="text-sm">{{ errorInfo.timestamp }}</div>
            </div>
            
            <div class="space-y-2">
              <div class="flex items-center gap-2 text-sm font-medium">
                <User class="w-4 h-4" />
                用户信息
              </div>
              <div class="text-sm">
                <div>用户ID: {{ error?.user_uuid || '未知' }}</div>
                <div>会话ID: {{ error?.session_id || '未知' }}</div>
              </div>
            </div>
            
            <div class="space-y-2">
              <div class="flex items-center gap-2 text-sm font-medium">
                <Monitor class="w-4 h-4" />
                设备信息
              </div>
              <div class="text-sm">
                <div>平台: {{ errorInfo.deviceInfo.platform }}</div>
                <div>浏览器: {{ errorInfo.deviceInfo.browser }}</div>
                <div>屏幕: {{ errorInfo.deviceInfo.screenSize }}</div>
              </div>
            </div>
          </div>
          
          <Separator />
          
          <!-- 页面URL -->
          <div class="space-y-2">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2 text-sm font-medium">
                <ExternalLink class="w-4 h-4" />
                页面URL
              </div>
              <div class="flex gap-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  @click="copyToClipboard(errorInfo.urlInfo.full, '页面URL')"
                >
                  <Copy class="w-3 h-3 mr-1" />
                  复制
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  @click="openUrl(errorInfo.urlInfo.full)"
                >
                  <ExternalLink class="w-3 h-3 mr-1" />
                  打开
                </Button>
              </div>
            </div>
            <div class="text-sm break-all bg-muted p-2 rounded">
              {{ errorInfo.urlInfo.full }}
            </div>
          </div>
          
          <Separator />
          
          <!-- 错误信息 -->
          <div class="space-y-2">
            <div class="flex items-center justify-between">
              <div class="text-sm font-medium">错误信息</div>
              <Button 
                variant="outline" 
                size="sm"
                @click="copyToClipboard(errorInfo.message, '错误信息')"
              >
                <Copy class="w-3 h-3 mr-1" />
                复制
              </Button>
            </div>
            <div class="text-sm bg-red-50 border border-red-200 p-3 rounded">
              {{ errorInfo.message }}
            </div>
          </div>
          
          <!-- 错误位置 -->
          <div v-if="errorInfo.hasLocation" class="space-y-2">
            <div class="text-sm font-medium">错误位置</div>
            <div class="text-sm bg-muted p-2 rounded">
              <div v-if="errorInfo.filename">文件: {{ errorInfo.filename }}</div>
              <div v-if="errorInfo.location">位置: {{ errorInfo.location }}</div>
            </div>
          </div>
          
          <!-- 错误堆栈 -->
          <div v-if="errorInfo.hasStack" class="space-y-2">
            <div class="flex items-center justify-between">
              <div class="text-sm font-medium">错误堆栈</div>
              <Button 
                variant="outline" 
                size="sm"
                @click="copyToClipboard(errorInfo.stack, '错误堆栈')"
              >
                <Copy class="w-3 h-3 mr-1" />
                复制
              </Button>
            </div>
            <pre class="text-xs bg-muted p-3 rounded overflow-auto max-h-40 whitespace-pre-wrap">{{ errorInfo.stack }}</pre>
          </div>
        </div>
      </ScrollArea>
    </DialogContent>
  </Dialog>
</template>
