<script setup lang="ts">
import type { MonitorEvent } from '@/api/monitor'
import { computed } from 'vue'
import { <PERSON><PERSON>, ExternalLink, Eye, Clock, User, Monitor, Globe } from 'lucide-vue-next'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { toast } from 'vue-sonner'
import {
  formatTimestamp,
  formatUrl,
  getDeviceInfo,
  getPageTitle,
  getEventParams,
} from '@/utils/eventDataMapper'

interface Props {
  open: boolean
  event: MonitorEvent | null
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:open': [value: boolean]
}>()

const pageViewInfo = computed(() => {
  if (!props.event) return null
  
  const deviceInfo = getDeviceInfo(props.event.event_data)
  const urlInfo = formatUrl(props.event.trigger_page_url)
  const title = getPageTitle(props.event.event_data)
  const params = getEventParams(props.event.event_data)
  
  // 页面访问相关信息
  const referrer = props.event.event_data.referrer || ''
  const action = props.event.event_data.action || 'page_view'
  const duration = props.event.event_data.duration || 0
  
  return {
    deviceInfo,
    urlInfo,
    title,
    params,
    referrer,
    action,
    duration,
    timestamp: formatTimestamp(props.event.trigger_time || 0),
  }
})

function copyToClipboard(text: string, label: string) {
  navigator.clipboard.writeText(text).then(() => {
    toast.success(`${label}已复制到剪贴板`)
  }).catch(() => {
    toast.error('复制失败')
  })
}

function openUrl(url: string) {
  window.open(url, '_blank')
}

function formatDuration(ms: number): string {
  if (ms < 1000) {
    return `${ms}ms`
  } else if (ms < 60000) {
    return `${(ms / 1000).toFixed(1)}s`
  } else {
    const minutes = Math.floor(ms / 60000)
    const seconds = ((ms % 60000) / 1000).toFixed(0)
    return `${minutes}m ${seconds}s`
  }
}

function getActionBadgeVariant(action: string) {
  switch (action) {
    case 'page_view':
      return 'default'
    case 'page_enter':
      return 'secondary'
    case 'page_leave':
      return 'outline'
    default:
      return 'secondary'
  }
}

function getActionLabel(action: string): string {
  const actionMap: Record<string, string> = {
    'page_view': '页面访问',
    'page_enter': '进入页面',
    'page_leave': '离开页面',
    'page_reload': '页面刷新',
  }
  return actionMap[action] || action
}
</script>

<template>
  <Dialog :open="open" @update:open="emit('update:open', $event)">
    <DialogContent class="max-w-4xl max-h-[90vh]">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <Eye class="w-5 h-5" />
          <span>页面访问详情</span>
          <Badge 
            v-if="pageViewInfo" 
            :variant="getActionBadgeVariant(pageViewInfo.action)"
          >
            {{ getActionLabel(pageViewInfo.action) }}
          </Badge>
        </DialogTitle>
      </DialogHeader>
      
      <ScrollArea v-if="pageViewInfo" class="max-h-[70vh]">
        <div class="space-y-6">
          <!-- 基本信息 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <div class="flex items-center gap-2 text-sm font-medium">
                <Clock class="w-4 h-4" />
                访问时间
              </div>
              <div class="text-sm">{{ pageViewInfo.timestamp }}</div>
            </div>
            
            <div class="space-y-2">
              <div class="flex items-center gap-2 text-sm font-medium">
                <User class="w-4 h-4" />
                用户信息
              </div>
              <div class="text-sm">
                <div>用户ID: {{ event?.user_uuid || '未知' }}</div>
                <div>会话ID: {{ event?.session_id || '未知' }}</div>
              </div>
            </div>
            
            <div class="space-y-2">
              <div class="flex items-center gap-2 text-sm font-medium">
                <Monitor class="w-4 h-4" />
                设备信息
              </div>
              <div class="text-sm">
                <div>平台: {{ pageViewInfo.deviceInfo.platform }}</div>
                <div>浏览器: {{ pageViewInfo.deviceInfo.browser }}</div>
                <div>屏幕: {{ pageViewInfo.deviceInfo.screenSize }}</div>
                <div>设备类型: {{ pageViewInfo.deviceInfo.deviceType }}</div>
              </div>
            </div>
            
            <div v-if="pageViewInfo.duration > 0" class="space-y-2">
              <div class="flex items-center gap-2 text-sm font-medium">
                <Clock class="w-4 h-4" />
                停留时长
              </div>
              <div class="text-sm font-medium text-blue-600">
                {{ formatDuration(pageViewInfo.duration) }}
              </div>
            </div>
          </div>
          
          <Separator />
          
          <!-- 页面信息 -->
          <div class="space-y-4">
            <!-- 页面标题 -->
            <div v-if="pageViewInfo.title" class="space-y-2">
              <div class="text-sm font-medium">页面标题</div>
              <div class="text-sm bg-muted p-2 rounded">
                {{ pageViewInfo.title }}
              </div>
            </div>
            
            <!-- 页面URL -->
            <div class="space-y-2">
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2 text-sm font-medium">
                  <ExternalLink class="w-4 h-4" />
                  页面URL
                </div>
                <div class="flex gap-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    @click="copyToClipboard(pageViewInfo.urlInfo.full, '页面URL')"
                  >
                    <Copy class="w-3 h-3 mr-1" />
                    复制
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    @click="openUrl(pageViewInfo.urlInfo.full)"
                  >
                    <ExternalLink class="w-3 h-3 mr-1" />
                    打开
                  </Button>
                </div>
              </div>
              <div class="text-sm break-all bg-muted p-2 rounded">
                {{ pageViewInfo.urlInfo.full }}
              </div>
            </div>
            
            <!-- 来源页面 -->
            <div v-if="pageViewInfo.referrer" class="space-y-2">
              <div class="flex items-center gap-2 text-sm font-medium">
                <Globe class="w-4 h-4" />
                来源页面
              </div>
              <div class="text-sm break-all bg-muted p-2 rounded">
                {{ pageViewInfo.referrer }}
              </div>
            </div>
          </div>
          
          <Separator />
          
          <!-- 页面参数 -->
          <div v-if="Object.keys(pageViewInfo.params).length > 0" class="space-y-2">
            <div class="text-sm font-medium">页面参数</div>
            <div class="grid grid-cols-1 gap-2">
              <div 
                v-for="(value, key) in pageViewInfo.params" 
                :key="key"
                class="flex justify-between items-center p-2 bg-muted rounded text-sm"
              >
                <span class="font-medium">{{ key }}:</span>
                <span class="text-muted-foreground">{{ value }}</span>
              </div>
            </div>
          </div>
          
          <!-- 完整事件数据 -->
          <div class="space-y-2">
            <div class="flex items-center justify-between">
              <div class="text-sm font-medium">完整事件数据</div>
              <Button 
                variant="outline" 
                size="sm"
                @click="copyToClipboard(JSON.stringify(event?.event_data, null, 2), '事件数据')"
              >
                <Copy class="w-3 h-3 mr-1" />
                复制JSON
              </Button>
            </div>
            <pre class="text-xs bg-muted p-3 rounded overflow-auto max-h-40">{{ JSON.stringify(event?.event_data, null, 2) }}</pre>
          </div>
        </div>
      </ScrollArea>
    </DialogContent>
  </Dialog>
</template>
