<script setup lang="ts">
import type { MonitorEvent } from '@/api/monitor'
import { computed } from 'vue'
import { Co<PERSON>, ExternalLink, Clock, Monitor, Gauge } from 'lucide-vue-next'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { toast } from 'vue-sonner'
import {
  formatTimestamp,
  formatUrl,
  getDeviceInfo,
  getPerformanceScore,
  formatPerformanceMetric,
  formatDuration,
} from '@/utils/eventDataMapper'
import PerformanceMetrics from './PerformanceMetrics.vue'

interface Props {
  open: boolean
  event: MonitorEvent | null
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:open': [value: boolean]
}>()

const performanceInfo = computed(() => {
  if (!props.event) return null
  
  const deviceInfo = getDeviceInfo(props.event.event_data)
  const urlInfo = formatUrl(props.event.trigger_page_url)
  const score = getPerformanceScore(props.event.event_data)
  
  // 核心性能指标
  const coreMetrics = [
    { key: 'fcp', label: '首次内容绘制', value: props.event.event_data.fcp },
    { key: 'lcp', label: '最大内容绘制', value: props.event.event_data.lcp },
    { key: 'fid', label: '首次输入延迟', value: props.event.event_data.fid },
    { key: 'cls', label: '累积布局偏移', value: props.event.event_data.cls },
  ].filter(metric => metric.value !== undefined && metric.value !== null)
  
  // 网络性能指标
  const networkMetrics = [
    { key: 'dns', label: 'DNS解析', value: props.event.event_data.dns },
    { key: 'tcp', label: 'TCP连接', value: props.event.event_data.tcp },
    { key: 'ssl', label: 'SSL握手', value: props.event.event_data.ssllink },
    { key: 'ttfb', label: '首字节时间', value: props.event.event_data.ttfb },
  ].filter(metric => metric.value !== undefined && metric.value !== null)
  
  // 页面加载指标
  const loadingMetrics = [
    { key: 'dom', label: 'DOM解析', value: props.event.event_data.dom },
    { key: 'load', label: '页面加载', value: props.event.event_data.loadon || props.event.event_data.load },
    { key: 'ready', label: 'DOM就绪', value: props.event.event_data.ready },
    { key: 'res', label: '资源加载', value: props.event.event_data.res },
  ].filter(metric => metric.value !== undefined && metric.value !== null)
  
  return {
    deviceInfo,
    urlInfo,
    score,
    coreMetrics,
    networkMetrics,
    loadingMetrics,
    timestamp: formatTimestamp(props.event.trigger_time || 0),
  }
})

function copyToClipboard(text: string, label: string) {
  navigator.clipboard.writeText(text).then(() => {
    toast.success(`${label}已复制到剪贴板`)
  }).catch(() => {
    toast.error('复制失败')
  })
}

function openUrl(url: string) {
  window.open(url, '_blank')
}

function getScoreColor(score: number): string {
  if (score >= 90) return 'text-green-600'
  if (score >= 75) return 'text-blue-600'
  if (score >= 50) return 'text-yellow-600'
  return 'text-red-600'
}

function getScoreBgColor(score: number): string {
  if (score >= 90) return 'bg-green-100'
  if (score >= 75) return 'bg-blue-100'
  if (score >= 50) return 'bg-yellow-100'
  return 'bg-red-100'
}
</script>

<template>
  <Dialog :open="open" @update:open="emit('update:open', $event)">
    <DialogContent class="max-w-4xl max-h-[90vh]">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <Gauge class="w-5 h-5" />
          <span>性能详情</span>
          <Badge 
            v-if="performanceInfo" 
            :class="[getScoreColor(performanceInfo.score.score), getScoreBgColor(performanceInfo.score.score)]"
          >
            {{ performanceInfo.score.label }} ({{ performanceInfo.score.score }}分)
          </Badge>
        </DialogTitle>
      </DialogHeader>
      
      <ScrollArea v-if="performanceInfo" class="max-h-[70vh]">
        <div class="space-y-6">
          <!-- 基本信息 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <div class="flex items-center gap-2 text-sm font-medium">
                <Clock class="w-4 h-4" />
                记录时间
              </div>
              <div class="text-sm">{{ performanceInfo.timestamp }}</div>
            </div>
            
            <div class="space-y-2">
              <div class="flex items-center gap-2 text-sm font-medium">
                <Monitor class="w-4 h-4" />
                设备信息
              </div>
              <div class="text-sm">
                <div>平台: {{ performanceInfo.deviceInfo.platform }}</div>
                <div>浏览器: {{ performanceInfo.deviceInfo.browser }}</div>
                <div>屏幕: {{ performanceInfo.deviceInfo.screenSize }}</div>
              </div>
            </div>
          </div>
          
          <Separator />
          
          <!-- 页面URL -->
          <div class="space-y-2">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2 text-sm font-medium">
                <ExternalLink class="w-4 h-4" />
                页面URL
              </div>
              <div class="flex gap-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  @click="copyToClipboard(performanceInfo.urlInfo.full, '页面URL')"
                >
                  <Copy class="w-3 h-3 mr-1" />
                  复制
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  @click="openUrl(performanceInfo.urlInfo.full)"
                >
                  <ExternalLink class="w-3 h-3 mr-1" />
                  打开
                </Button>
              </div>
            </div>
            <div class="text-sm break-all bg-muted p-2 rounded">
              {{ performanceInfo.urlInfo.full }}
            </div>
          </div>
          
          <Separator />
          
          <!-- 性能指标组件 -->
          <PerformanceMetrics :event-data="event?.event_data" />
          
          <Separator />
          
          <!-- 核心Web指标详情 -->
          <div v-if="performanceInfo.coreMetrics.length > 0">
            <h4 class="text-sm font-medium mb-3">核心Web指标详情</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div 
                v-for="metric in performanceInfo.coreMetrics" 
                :key="metric.key"
                class="p-3 bg-muted rounded"
              >
                <div class="flex justify-between items-center mb-1">
                  <span class="text-sm font-medium">{{ metric.label }}</span>
                  <span class="text-sm font-bold">
                    {{ formatPerformanceMetric(metric.key, metric.value).value }}{{ formatPerformanceMetric(metric.key, metric.value).unit }}
                  </span>
                </div>
                <div class="text-xs text-muted-foreground">
                  {{ metric.key === 'fcp' ? '用户看到首个内容的时间' : 
                     metric.key === 'lcp' ? '最大内容元素渲染时间' :
                     metric.key === 'fid' ? '用户首次交互响应时间' :
                     metric.key === 'cls' ? '页面布局稳定性指标' : '' }}
                </div>
              </div>
            </div>
          </div>
          
          <!-- 网络性能详情 -->
          <div v-if="performanceInfo.networkMetrics.length > 0">
            <h4 class="text-sm font-medium mb-3">网络性能详情</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
              <div 
                v-for="metric in performanceInfo.networkMetrics" 
                :key="metric.key"
                class="flex justify-between items-center text-sm p-2 bg-muted rounded"
              >
                <span class="text-muted-foreground">{{ metric.label }}</span>
                <span class="font-medium">{{ formatDuration(metric.value) }}</span>
              </div>
            </div>
          </div>
          
          <!-- 页面加载详情 -->
          <div v-if="performanceInfo.loadingMetrics.length > 0">
            <h4 class="text-sm font-medium mb-3">页面加载详情</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
              <div 
                v-for="metric in performanceInfo.loadingMetrics" 
                :key="metric.key"
                class="flex justify-between items-center text-sm p-2 bg-muted rounded"
              >
                <span class="text-muted-foreground">{{ metric.label }}</span>
                <span class="font-medium">{{ formatDuration(metric.value) }}</span>
              </div>
            </div>
          </div>
        </div>
      </ScrollArea>
    </DialogContent>
  </Dialog>
</template>
