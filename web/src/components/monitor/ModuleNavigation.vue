<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import {
  <PERSON>ertTriangle,
  Zap,
  Eye,
  Users,
  Setting<PERSON>,
  MousePointer,
  ArrowRight
} from 'lucide-vue-next'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import ResponsiveGrid from './ResponsiveGrid.vue'
import type { EventStatistics } from '@/api/monitor'

interface Props {
  projectId: string
  statistics?: EventStatistics | null
}

const props = defineProps<Props>()

const router = useRouter()

const modules = computed(() => [
  {
    key: 'errors',
    title: '错误监控',
    description: '查看应用错误和异常',
    icon: AlertTriangle,
    iconColor: 'text-red-500',
    bgColor: 'bg-red-100',
    count: props.statistics?.error || 0,
    countColor: 'text-red-600',
    path: `/monitor/${props.projectId}/errors`,
  },
  {
    key: 'performance',
    title: '性能监控',
    description: '分析页面加载性能',
    icon: Zap,
    iconColor: 'text-yellow-500',
    bgColor: 'bg-yellow-100',
    count: props.statistics?.performance || 0,
    countColor: 'text-yellow-600',
    path: `/monitor/${props.projectId}/performance`,
  },
  {
    key: 'page-views',
    title: '页面访问',
    description: '统计页面访问情况',
    icon: Eye,
    iconColor: 'text-blue-500',
    bgColor: 'bg-blue-100',
    count: props.statistics?.pv || 0,
    countColor: 'text-blue-600',
    path: `/monitor/${props.projectId}/page-views`,
  },
  {
    key: 'sessions',
    title: '用户会话',
    description: '分析用户会话数据',
    icon: Users,
    iconColor: 'text-green-500',
    bgColor: 'bg-green-100',
    count: 0, // 会话数据需要单独获取
    countColor: 'text-green-600',
    path: `/monitor/${props.projectId}/sessions`,
  },
  {
    key: 'custom-events',
    title: '自定义事件',
    description: '查看自定义埋点数据',
    icon: Settings,
    iconColor: 'text-purple-500',
    bgColor: 'bg-purple-100',
    count: props.statistics?.custom || 0,
    countColor: 'text-purple-600',
    path: `/monitor/${props.projectId}/custom-events`,
  },
  {
    key: 'user-behavior',
    title: '用户行为',
    description: '分析用户交互行为',
    icon: MousePointer,
    iconColor: 'text-indigo-500',
    bgColor: 'bg-indigo-100',
    count: props.statistics?.click || 0,
    countColor: 'text-indigo-600',
    path: `/monitor/${props.projectId}/user-behavior`,
  },
])

function navigateTo(path: string) {
  router.push(path)
}

function formatCount(count: number): string {
  if (count >= 1000000) {
    return (count / 1000000).toFixed(1) + 'M'
  } else if (count >= 1000) {
    return (count / 1000).toFixed(1) + 'K'
  }
  return count.toString()
}
</script>

<template>
  <ResponsiveGrid
    :cols="{ xs: 1, sm: 2, md: 2, lg: 3 }"
    gap="lg"
  >
    <Card
      v-for="module in modules"
      :key="module.key"
      class="cursor-pointer hover:shadow-lg transition-all duration-200 hover:scale-[1.02] group"
      @click="navigateTo(module.path)"
    >
      <CardHeader class="text-center pb-4">
        <div class="relative">
          <!-- 图标容器 -->
          <div
            class="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center transition-transform group-hover:scale-110"
            :class="module.bgColor"
          >
            <component
              :is="module.icon"
              class="w-8 h-8"
              :class="module.iconColor"
            />
          </div>

          <!-- 数量徽章 -->
          <Badge
            v-if="module.count > 0"
            class="absolute -top-1 -right-1 min-w-[24px] h-6 flex items-center justify-center text-xs font-bold"
            :class="module.countColor"
            variant="secondary"
          >
            {{ formatCount(module.count) }}
          </Badge>
        </div>

        <div class="space-y-2">
          <CardTitle class="flex items-center justify-center gap-2 group-hover:text-primary transition-colors text-base">
            {{ module.title }}
            <ArrowRight class="w-4 h-4 opacity-0 group-hover:opacity-100 transition-opacity" />
          </CardTitle>
          <CardDescription class="text-sm">
            {{ module.description }}
          </CardDescription>
        </div>
      </CardHeader>

      <CardContent class="pt-0">
        <div class="text-center">
          <div class="text-2xl font-bold mb-1" :class="module.countColor">
            {{ formatCount(module.count) }}
          </div>
          <div class="text-xs text-muted-foreground">
            {{ module.key === 'sessions' ? '活跃会话' : '事件总数' }}
          </div>
        </div>
      </CardContent>
    </Card>
  </ResponsiveGrid>
</template>
