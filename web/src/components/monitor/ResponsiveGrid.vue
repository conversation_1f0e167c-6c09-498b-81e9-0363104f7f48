<script setup lang="ts">
import { computed } from 'vue'
import { useWindowSize } from '@vueuse/core'

interface Props {
  /**
   * 列数配置
   * - 数字: 固定列数
   * - 对象: 响应式列数配置
   */
  cols?: number | {
    xs?: number  // < 640px
    sm?: number  // >= 640px
    md?: number  // >= 768px
    lg?: number  // >= 1024px
    xl?: number  // >= 1280px
  }
  /**
   * 间距大小
   */
  gap?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  /**
   * 自定义类名
   */
  class?: string
  /**
   * 是否自动填充
   */
  autoFit?: boolean
  /**
   * 最小列宽（当autoFit为true时使用）
   */
  minColWidth?: string
}

const props = withDefaults(defineProps<Props>(), {
  cols: { xs: 1, sm: 2, md: 3, lg: 4 },
  gap: 'md',
  autoFit: false,
  minColWidth: '250px',
})

const { width } = useWindowSize()

const currentCols = computed(() => {
  if (typeof props.cols === 'number') {
    return props.cols
  }
  
  const breakpoints = props.cols
  
  if (width.value >= 1280 && breakpoints.xl) return breakpoints.xl
  if (width.value >= 1024 && breakpoints.lg) return breakpoints.lg
  if (width.value >= 768 && breakpoints.md) return breakpoints.md
  if (width.value >= 640 && breakpoints.sm) return breakpoints.sm
  return breakpoints.xs || 1
})

const gapClasses = computed(() => {
  const gapMap = {
    xs: 'gap-2',
    sm: 'gap-3',
    md: 'gap-4',
    lg: 'gap-6',
    xl: 'gap-8',
  }
  return gapMap[props.gap]
})

const gridClasses = computed(() => {
  const classes = ['responsive-grid', gapClasses.value]
  
  if (props.autoFit) {
    classes.push('grid-auto-fit')
  } else {
    // 使用Tailwind的grid-cols类
    const colsClass = `grid-cols-${currentCols.value}`
    classes.push(colsClass)
  }
  
  if (props.class) {
    classes.push(props.class)
  }
  
  return classes.join(' ')
})

const gridStyle = computed(() => {
  if (props.autoFit) {
    return {
      gridTemplateColumns: `repeat(auto-fit, minmax(${props.minColWidth}, 1fr))`,
    }
  }
  return {}
})
</script>

<template>
  <div 
    :class="gridClasses"
    :style="gridStyle"
  >
    <slot 
      :current-cols="currentCols"
      :width="width"
    />
  </div>
</template>

<style scoped>
.responsive-grid {
  display: grid;
  width: 100%;
}

.grid-auto-fit {
  /* 自动填充网格 */
}

/* 确保在所有屏幕尺寸下都有合适的列数 */
@media (max-width: 639px) {
  .responsive-grid {
    grid-template-columns: 1fr !important;
  }
}

/* 动态生成grid-cols类 */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }

/* 响应式断点优化 */
@media (min-width: 640px) {
  .responsive-grid {
    /* 小屏幕优化 */
  }
}

@media (min-width: 768px) {
  .responsive-grid {
    /* 中等屏幕优化 */
  }
}

@media (min-width: 1024px) {
  .responsive-grid {
    /* 大屏幕优化 */
  }
}

@media (min-width: 1280px) {
  .responsive-grid {
    /* 超大屏幕优化 */
  }
}
</style>
