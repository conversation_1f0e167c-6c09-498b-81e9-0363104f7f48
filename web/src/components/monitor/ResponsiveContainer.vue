<script setup lang="ts">
import { computed } from 'vue'
import { useWindowSize } from '@vueuse/core'

interface Props {
  /**
   * 容器类型
   * - page: 页面容器，有内边距
   * - section: 区块容器，较小内边距
   * - card: 卡片容器，无内边距
   */
  type?: 'page' | 'section' | 'card'
  /**
   * 最大宽度
   */
  maxWidth?: string
  /**
   * 是否居中
   */
  centered?: boolean
  /**
   * 自定义类名
   */
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'page',
  maxWidth: 'none',
  centered: false,
})

const { width } = useWindowSize()

const isMobile = computed(() => width.value < 768)
const isTablet = computed(() => width.value >= 768 && width.value < 1024)
const isDesktop = computed(() => width.value >= 1024)

const containerClasses = computed(() => {
  const classes = ['responsive-container']
  
  // 基础类型样式
  switch (props.type) {
    case 'page':
      classes.push('px-4 py-6 md:px-6 md:py-8 lg:px-8')
      break
    case 'section':
      classes.push('px-2 py-3 md:px-4 md:py-4')
      break
    case 'card':
      classes.push('p-0')
      break
  }
  
  // 居中样式
  if (props.centered) {
    classes.push('mx-auto')
  }
  
  // 自定义类名
  if (props.class) {
    classes.push(props.class)
  }
  
  return classes.join(' ')
})

const containerStyle = computed(() => {
  const style: Record<string, string> = {}
  
  if (props.maxWidth !== 'none') {
    style.maxWidth = props.maxWidth
  }
  
  return style
})
</script>

<template>
  <div 
    :class="containerClasses" 
    :style="containerStyle"
  >
    <slot 
      :is-mobile="isMobile"
      :is-tablet="isTablet" 
      :is-desktop="isDesktop"
      :width="width"
    />
  </div>
</template>

<style scoped>
.responsive-container {
  width: 100%;
  transition: padding 0.2s ease-in-out;
}

/* 移动端优化 */
@media (max-width: 767px) {
  .responsive-container {
    /* 确保在小屏幕上有足够的触摸区域 */
    min-height: 44px;
  }
}

/* 平板优化 */
@media (min-width: 768px) and (max-width: 1023px) {
  .responsive-container {
    /* 平板特定样式 */
  }
}

/* 桌面优化 */
@media (min-width: 1024px) {
  .responsive-container {
    /* 桌面特定样式 */
  }
}
</style>
