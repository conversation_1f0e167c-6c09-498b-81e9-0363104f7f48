<script setup lang="ts">
import type { UserSession } from '@/api/monitor'
import { Activity, AlertTriangle, Clock, Copy, Eye, Monitor, User } from 'lucide-vue-next'
import { computed } from 'vue'
import { toast } from 'vue-sonner'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Progress } from '@/components/ui/progress'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import {
  formatDuration,
  formatTimestamp,
} from '@/utils/eventDataMapper'

interface Props {
  open: boolean
  session: UserSession | null
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:open': [value: boolean]
  'viewEvents': [sessionId: string]
}>()

const sessionInfo = computed(() => {
  if (!props.session)
    return null

  const duration = props.session.duration || 0
  const errorRate = props.session.event_count > 0 ? (props.session.errors / props.session.event_count * 100) : 0

  // 会话质量评估
  let quality: 'excellent' | 'good' | 'poor' = 'good'
  if (errorRate > 10 || duration < 30000) {
    quality = 'poor'
  }
  else if (errorRate < 2 && duration > 300000) {
    quality = 'excellent'
  }

  return {
    duration,
    errorRate,
    quality,
    firstVisit: formatTimestamp(props.session.first_visit),
    lastVisit: formatTimestamp(props.session.last_visit),
  }
})

function copyToClipboard(text: string, label: string) {
  navigator.clipboard.writeText(text).then(() => {
    toast.success(`${label}已复制到剪贴板`)
  }).catch(() => {
    toast.error('复制失败')
  })
}

function getQualityColor(quality: 'excellent' | 'good' | 'poor'): string {
  const colorMap = {
    excellent: 'text-green-600',
    good: 'text-blue-600',
    poor: 'text-red-600',
  }
  return colorMap[quality]
}

function getQualityBgColor(quality: 'excellent' | 'good' | 'poor'): string {
  const colorMap = {
    excellent: 'bg-green-100',
    good: 'bg-blue-100',
    poor: 'bg-red-100',
  }
  return colorMap[quality]
}

function getQualityLabel(quality: 'excellent' | 'good' | 'poor'): string {
  const labelMap = {
    excellent: '优秀',
    good: '良好',
    poor: '较差',
  }
  return labelMap[quality]
}

function getPlatformIcon(platform: string) {
  if (platform.toLowerCase().includes('windows'))
    return '🖥️'
  if (platform.toLowerCase().includes('mac'))
    return '🍎'
  if (platform.toLowerCase().includes('linux'))
    return '🐧'
  if (platform.toLowerCase().includes('android'))
    return '🤖'
  if (platform.toLowerCase().includes('ios'))
    return '📱'
  return '💻'
}

function handleViewEvents() {
  if (props.session) {
    emit('viewEvents', props.session.session_id)
  }
}
</script>

<template>
  <Dialog :open="open" @update:open="emit('update:open', $event)">
    <DialogContent class="max-w-4xl max-h-[90vh]">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <User class="w-5 h-5" />
          <span>用户会话详情</span>
          <Badge
            v-if="sessionInfo"
            :class="[getQualityColor(sessionInfo.quality), getQualityBgColor(sessionInfo.quality)]"
          >
            {{ getQualityLabel(sessionInfo.quality) }}
          </Badge>
        </DialogTitle>
      </DialogHeader>

      <ScrollArea v-if="sessionInfo && session" class="max-h-[70vh]">
        <div class="space-y-6">
          <!-- 基本信息 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <div class="flex items-center gap-2 text-sm font-medium">
                <User class="w-4 h-4" />
                用户标识
              </div>
              <div class="text-sm space-y-1">
                <div class="flex items-center gap-2">
                  <span class="text-muted-foreground">用户ID:</span>
                  <code class="text-xs bg-muted px-1 rounded">{{ session.user_uuid }}</code>
                  <Button
                    variant="ghost"
                    size="sm"
                    @click="copyToClipboard(session.user_uuid, '用户ID')"
                  >
                    <Copy class="w-3 h-3" />
                  </Button>
                </div>
                <div class="flex items-center gap-2">
                  <span class="text-muted-foreground">会话ID:</span>
                  <code class="text-xs bg-muted px-1 rounded">{{ session.session_id }}</code>
                  <Button
                    variant="ghost"
                    size="sm"
                    @click="copyToClipboard(session.session_id, '会话ID')"
                  >
                    <Copy class="w-3 h-3" />
                  </Button>
                </div>
                <div class="flex items-center gap-2">
                  <span class="text-muted-foreground">设备ID:</span>
                  <code class="text-xs bg-muted px-1 rounded">{{ session.device_id }}</code>
                </div>
              </div>
            </div>

            <div class="space-y-2">
              <div class="flex items-center gap-2 text-sm font-medium">
                <Monitor class="w-4 h-4" />
                设备信息
              </div>
              <div class="text-sm">
                <div class="flex items-center gap-2">
                  <span>{{ getPlatformIcon(session.platform) }}</span>
                  <span>{{ session.platform }}</span>
                </div>
              </div>
            </div>

            <div class="space-y-2">
              <div class="flex items-center gap-2 text-sm font-medium">
                <Clock class="w-4 h-4" />
                时间信息
              </div>
              <div class="text-sm space-y-1">
                <div>首次访问: {{ sessionInfo.firstVisit }}</div>
                <div>最后访问: {{ sessionInfo.lastVisit }}</div>
                <div class="flex items-center gap-2">
                  <span>会话时长:</span>
                  <Badge variant="outline">
                    {{ formatDuration(sessionInfo.duration) }}
                  </Badge>
                </div>
              </div>
            </div>

            <div class="space-y-2">
              <div class="flex items-center gap-2 text-sm font-medium">
                <Activity class="w-4 h-4" />
                活动统计
              </div>
              <div class="text-sm space-y-1">
                <div>总事件数: {{ session.event_count }}</div>
                <div>页面访问: {{ session.page_views }}</div>
                <div class="flex items-center gap-2">
                  <span>错误数量:</span>
                  <Badge :variant="session.errors > 0 ? 'destructive' : 'secondary'">
                    {{ session.errors }}
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          <!-- 会话质量分析 -->
          <div class="space-y-4">
            <h4 class="text-sm font-medium">
              会话质量分析
            </h4>

            <!-- 错误率 -->
            <div class="space-y-2">
              <div class="flex justify-between text-sm">
                <span>错误率</span>
                <span :class="sessionInfo.errorRate > 5 ? 'text-red-600' : 'text-green-600'">
                  {{ sessionInfo.errorRate.toFixed(1) }}%
                </span>
              </div>
              <Progress
                :value="Math.min(sessionInfo.errorRate, 20)"
                :class="sessionInfo.errorRate > 5 ? 'bg-red-100' : 'bg-green-100'"
              />
            </div>

            <!-- 活跃度 -->
            <div class="space-y-2">
              <div class="flex justify-between text-sm">
                <span>活跃度 (事件/分钟)</span>
                <span>{{ (session.event_count / (sessionInfo.duration / 60000)).toFixed(1) }}</span>
              </div>
              <Progress
                :value="Math.min((session.event_count / (sessionInfo.duration / 60000)) * 10, 100)"
                class="bg-blue-100"
              />
            </div>
          </div>

          <Separator />

          <!-- 快速操作 -->
          <div class="space-y-3">
            <h4 class="text-sm font-medium">
              快速操作
            </h4>
            <div class="flex flex-wrap gap-2">
              <Button variant="outline" size="sm" @click="handleViewEvents">
                <Eye class="w-4 h-4 mr-2" />
                查看会话事件
              </Button>
              <Button
                v-if="session.errors > 0"
                variant="outline"
                size="sm"
                @click="$emit('viewEvents', `${session.session_id}?filter=error`)"
              >
                <AlertTriangle class="w-4 h-4 mr-2" />
                查看错误事件
              </Button>
            </div>
          </div>

          <!-- 会话摘要 -->
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="text-center p-3 bg-muted rounded">
              <div class="text-2xl font-bold text-blue-600">
                {{ session.page_views }}
              </div>
              <div class="text-xs text-muted-foreground">
                页面访问
              </div>
            </div>
            <div class="text-center p-3 bg-muted rounded">
              <div class="text-2xl font-bold text-green-600">
                {{ session.event_count }}
              </div>
              <div class="text-xs text-muted-foreground">
                总事件数
              </div>
            </div>
            <div class="text-center p-3 bg-muted rounded">
              <div class="text-2xl font-bold" :class="session.errors > 0 ? 'text-red-600' : 'text-gray-600'">
                {{ session.errors }}
              </div>
              <div class="text-xs text-muted-foreground">
                错误数量
              </div>
            </div>
            <div class="text-center p-3 bg-muted rounded">
              <div class="text-2xl font-bold text-purple-600">
                {{ formatDuration(sessionInfo.duration) }}
              </div>
              <div class="text-xs text-muted-foreground">
                会话时长
              </div>
            </div>
          </div>
        </div>
      </ScrollArea>
    </DialogContent>
  </Dialog>
</template>
