<script setup lang="ts">
import { computed } from 'vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'

interface Props {
  title: string
  description?: string
  value: string | number
  unit?: string
  trend?: {
    value: number
    label: string
    direction: 'up' | 'down' | 'neutral'
  }
  status?: 'success' | 'warning' | 'error' | 'info'
  icon?: any
  loading?: boolean
  clickable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  clickable: false,
})

const emit = defineEmits<{
  click: []
}>()

const statusColors = computed(() => {
  const colorMap = {
    success: 'text-green-600',
    warning: 'text-yellow-600',
    error: 'text-red-600',
    info: 'text-blue-600',
  }
  return props.status ? colorMap[props.status] : 'text-foreground'
})

const trendColors = computed(() => {
  if (!props.trend) return ''
  
  const colorMap = {
    up: 'text-green-600',
    down: 'text-red-600',
    neutral: 'text-gray-600',
  }
  return colorMap[props.trend.direction]
})

const cardClasses = computed(() => {
  return [
    'transition-all duration-200',
    props.clickable ? 'cursor-pointer hover:shadow-md hover:scale-[1.02]' : '',
  ].filter(Boolean).join(' ')
})

function handleClick() {
  if (props.clickable) {
    emit('click')
  }
}
</script>

<template>
  <Card :class="cardClasses" @click="handleClick">
    <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
      <CardTitle class="text-sm font-medium">
        {{ title }}
      </CardTitle>
      <component :is="icon" v-if="icon" class="h-4 w-4 text-muted-foreground" />
    </CardHeader>
    <CardContent>
      <div v-if="loading" class="space-y-2">
        <Skeleton class="h-8 w-20" />
        <Skeleton class="h-4 w-32" />
      </div>
      <div v-else>
        <div class="flex items-baseline space-x-2">
          <div class="text-2xl font-bold" :class="statusColors">
            {{ value }}
          </div>
          <span v-if="unit" class="text-sm text-muted-foreground">
            {{ unit }}
          </span>
        </div>
        
        <div class="flex items-center justify-between mt-2">
          <p v-if="description" class="text-xs text-muted-foreground">
            {{ description }}
          </p>
          
          <div v-if="trend" class="flex items-center space-x-1">
            <Badge 
              variant="outline" 
              :class="trendColors"
              class="text-xs"
            >
              <span v-if="trend.direction === 'up'">↗</span>
              <span v-else-if="trend.direction === 'down'">↘</span>
              <span v-else>→</span>
              {{ trend.value }}%
            </Badge>
            <span class="text-xs text-muted-foreground">
              {{ trend.label }}
            </span>
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
</template>
