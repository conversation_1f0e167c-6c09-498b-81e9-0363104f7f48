import { computed, ref, watch, type Ref } from 'vue'
import { calculateVirtualScrollItems } from '@/utils/performance'

interface VirtualScrollOptions {
  itemHeight: number
  containerHeight: number
  overscan?: number
}

interface VirtualScrollItem<T> {
  index: number
  data: T
  offsetY: number
}

/**
 * 虚拟滚动 composable
 * 用于优化大列表的渲染性能
 */
export function useVirtualScroll<T>(
  items: Ref<T[]>,
  options: VirtualScrollOptions
) {
  const scrollTop = ref(0)
  const { itemHeight, containerHeight, overscan = 5 } = options

  // 计算可见项目
  const visibleItems = computed(() => {
    const totalItems = items.value.length
    if (totalItems === 0) return []

    const {
      startIndex,
      endIndex,
      offsetY,
    } = calculateVirtualScrollItems(
      containerHeight,
      itemHeight,
      scrollTop.value,
      totalItems,
      overscan
    )

    const result: VirtualScrollItem<T>[] = []
    for (let i = startIndex; i <= endIndex; i++) {
      if (i < totalItems) {
        result.push({
          index: i,
          data: items.value[i],
          offsetY: offsetY + (i - startIndex) * itemHeight,
        })
      }
    }

    return result
  })

  // 总高度
  const totalHeight = computed(() => items.value.length * itemHeight)

  // 容器样式
  const containerStyle = computed(() => ({
    height: `${containerHeight}px`,
    overflow: 'auto',
  }))

  // 内容样式
  const contentStyle = computed(() => ({
    height: `${totalHeight.value}px`,
    position: 'relative' as const,
  }))

  // 滚动处理
  function handleScroll(event: Event) {
    const target = event.target as HTMLElement
    scrollTop.value = target.scrollTop
  }

  // 滚动到指定项目
  function scrollToItem(index: number) {
    const targetScrollTop = index * itemHeight
    scrollTop.value = Math.max(0, Math.min(targetScrollTop, totalHeight.value - containerHeight))
  }

  // 滚动到顶部
  function scrollToTop() {
    scrollTop.value = 0
  }

  // 滚动到底部
  function scrollToBottom() {
    scrollTop.value = Math.max(0, totalHeight.value - containerHeight)
  }

  return {
    visibleItems,
    totalHeight,
    containerStyle,
    contentStyle,
    scrollTop: computed(() => scrollTop.value),
    handleScroll,
    scrollToItem,
    scrollToTop,
    scrollToBottom,
  }
}
