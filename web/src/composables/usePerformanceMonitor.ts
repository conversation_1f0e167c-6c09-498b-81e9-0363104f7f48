import { ref, onMounted, onUnmounted } from 'vue'
import { performanceTracker, getMemoryUsage } from '@/utils/performance'

interface PerformanceMetrics {
  renderTime: number
  memoryUsage: {
    used: number
    total: number
    percentage: number
  } | null
  fps: number
  componentCount: number
}

/**
 * 性能监控 composable
 * 用于监控组件和页面的性能指标
 */
export function usePerformanceMonitor(componentName?: string) {
  const metrics = ref<PerformanceMetrics>({
    renderTime: 0,
    memoryUsage: null,
    fps: 0,
    componentCount: 0,
  })

  const isMonitoring = ref(false)
  let frameCount = 0
  let lastTime = performance.now()
  let animationId: number | null = null

  // FPS 计算
  function calculateFPS() {
    frameCount++
    const currentTime = performance.now()
    
    if (currentTime - lastTime >= 1000) {
      metrics.value.fps = Math.round((frameCount * 1000) / (currentTime - lastTime))
      frameCount = 0
      lastTime = currentTime
    }
    
    if (isMonitoring.value) {
      animationId = requestAnimationFrame(calculateFPS)
    }
  }

  // 开始监控
  function startMonitoring() {
    if (isMonitoring.value) return
    
    isMonitoring.value = true
    
    // 开始性能标记
    if (componentName) {
      performanceTracker.start(`${componentName}-render`)
    }
    
    // 开始 FPS 监控
    calculateFPS()
    
    // 定期更新内存使用情况
    const memoryInterval = setInterval(() => {
      if (!isMonitoring.value) {
        clearInterval(memoryInterval)
        return
      }
      
      metrics.value.memoryUsage = getMemoryUsage()
    }, 1000)
  }

  // 停止监控
  function stopMonitoring() {
    isMonitoring.value = false
    
    if (animationId) {
      cancelAnimationFrame(animationId)
      animationId = null
    }
    
    // 结束性能标记
    if (componentName) {
      const renderTime = performanceTracker.end(`${componentName}-render`)
      metrics.value.renderTime = renderTime
    }
  }

  // 记录组件渲染时间
  function measureRender<T>(fn: () => T): T {
    const startTime = performance.now()
    const result = fn()
    const endTime = performance.now()
    metrics.value.renderTime = endTime - startTime
    return result
  }

  // 记录异步操作时间
  async function measureAsync<T>(fn: () => Promise<T>, label?: string): Promise<T> {
    const markName = label || 'async-operation'
    performanceTracker.start(markName)
    
    try {
      const result = await fn()
      const duration = performanceTracker.end(markName)
      console.log(`${markName} took ${duration.toFixed(2)}ms`)
      return result
    } catch (error) {
      performanceTracker.end(markName)
      throw error
    }
  }

  // 获取性能报告
  function getPerformanceReport(): {
    summary: string
    details: PerformanceMetrics
    recommendations: string[]
  } {
    const { renderTime, memoryUsage, fps } = metrics.value
    const recommendations: string[] = []
    
    // 渲染时间建议
    if (renderTime > 16) {
      recommendations.push('渲染时间过长，考虑优化组件渲染逻辑')
    }
    
    // FPS 建议
    if (fps < 30) {
      recommendations.push('帧率较低，检查是否有性能瓶颈')
    }
    
    // 内存使用建议
    if (memoryUsage && memoryUsage.percentage > 80) {
      recommendations.push('内存使用率较高，检查是否有内存泄漏')
    }
    
    const summary = `渲染时间: ${renderTime.toFixed(2)}ms, FPS: ${fps}, 内存使用: ${memoryUsage?.percentage.toFixed(1) || 'N/A'}%`
    
    return {
      summary,
      details: metrics.value,
      recommendations,
    }
  }

  // 生命周期钩子
  onMounted(() => {
    startMonitoring()
  })

  onUnmounted(() => {
    stopMonitoring()
  })

  return {
    metrics,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    measureRender,
    measureAsync,
    getPerformanceReport,
  }
}

/**
 * 页面性能监控 composable
 * 监控整个页面的性能指标
 */
export function usePagePerformance() {
  const pageMetrics = ref({
    loadTime: 0,
    domContentLoaded: 0,
    firstContentfulPaint: 0,
    largestContentfulPaint: 0,
    firstInputDelay: 0,
    cumulativeLayoutShift: 0,
  })

  // 获取页面性能指标
  function collectPageMetrics() {
    if ('performance' in window) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      
      if (navigation) {
        pageMetrics.value.loadTime = navigation.loadEventEnd - navigation.loadEventStart
        pageMetrics.value.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart
      }
      
      // Web Vitals
      if ('PerformanceObserver' in window) {
        // FCP
        new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const fcp = entries.find(entry => entry.name === 'first-contentful-paint')
          if (fcp) {
            pageMetrics.value.firstContentfulPaint = fcp.startTime
          }
        }).observe({ entryTypes: ['paint'] })
        
        // LCP
        new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const lastEntry = entries[entries.length - 1]
          if (lastEntry) {
            pageMetrics.value.largestContentfulPaint = lastEntry.startTime
          }
        }).observe({ entryTypes: ['largest-contentful-paint'] })
        
        // FID
        new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const firstEntry = entries[0]
          if (firstEntry) {
            pageMetrics.value.firstInputDelay = firstEntry.processingStart - firstEntry.startTime
          }
        }).observe({ entryTypes: ['first-input'] })
        
        // CLS
        new PerformanceObserver((list) => {
          let clsValue = 0
          const entries = list.getEntries()
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value
            }
          })
          pageMetrics.value.cumulativeLayoutShift = clsValue
        }).observe({ entryTypes: ['layout-shift'] })
      }
    }
  }

  onMounted(() => {
    // 延迟收集指标，确保页面完全加载
    setTimeout(collectPageMetrics, 1000)
  })

  return {
    pageMetrics,
    collectPageMetrics,
  }
}
