import { ref, computed, watch, type Ref } from 'vue'
import { debounce } from '@/utils/performance'

interface AsyncDataOptions<T> {
  /**
   * 初始数据
   */
  initialData?: T
  /**
   * 是否立即执行
   */
  immediate?: boolean
  /**
   * 防抖延迟时间（毫秒）
   */
  debounceMs?: number
  /**
   * 重试次数
   */
  retryCount?: number
  /**
   * 重试延迟时间（毫秒）
   */
  retryDelay?: number
  /**
   * 缓存键
   */
  cacheKey?: string
  /**
   * 缓存过期时间（毫秒）
   */
  cacheExpiry?: number
}

interface AsyncDataReturn<T> {
  data: Ref<T | null>
  loading: Ref<boolean>
  error: Ref<Error | null>
  execute: () => Promise<void>
  refresh: () => Promise<void>
  clear: () => void
}

// 简单的内存缓存
const cache = new Map<string, { data: any; timestamp: number }>()

/**
 * 异步数据加载 composable
 * 提供加载状态、错误处理、重试、缓存等功能
 */
export function useAsyncData<T>(
  fetcher: () => Promise<T>,
  options: AsyncDataOptions<T> = {}
): AsyncDataReturn<T> {
  const {
    initialData = null,
    immediate = true,
    debounceMs = 0,
    retryCount = 0,
    retryDelay = 1000,
    cacheKey,
    cacheExpiry = 5 * 60 * 1000, // 5分钟
  } = options

  const data = ref<T | null>(initialData) as Ref<T | null>
  const loading = ref(false)
  const error = ref<Error | null>(null)

  // 检查缓存
  function checkCache(): T | null {
    if (!cacheKey) return null
    
    const cached = cache.get(cacheKey)
    if (!cached) return null
    
    const isExpired = Date.now() - cached.timestamp > cacheExpiry
    if (isExpired) {
      cache.delete(cacheKey)
      return null
    }
    
    return cached.data
  }

  // 设置缓存
  function setCache(value: T) {
    if (cacheKey) {
      cache.set(cacheKey, {
        data: value,
        timestamp: Date.now(),
      })
    }
  }

  // 执行数据获取
  async function executeInternal(): Promise<void> {
    // 检查缓存
    const cachedData = checkCache()
    if (cachedData) {
      data.value = cachedData
      return
    }

    loading.value = true
    error.value = null

    let lastError: Error | null = null
    let attempts = 0

    while (attempts <= retryCount) {
      try {
        const result = await fetcher()
        data.value = result
        setCache(result)
        loading.value = false
        return
      } catch (err) {
        lastError = err instanceof Error ? err : new Error(String(err))
        attempts++
        
        if (attempts <= retryCount) {
          await new Promise(resolve => setTimeout(resolve, retryDelay))
        }
      }
    }

    error.value = lastError
    loading.value = false
  }

  // 创建防抖版本的执行函数
  const debouncedExecute = debounceMs > 0 
    ? debounce(executeInternal, debounceMs)
    : executeInternal

  // 执行函数
  const execute = async (): Promise<void> => {
    return debouncedExecute()
  }

  // 刷新函数（忽略缓存）
  const refresh = async (): Promise<void> => {
    if (cacheKey) {
      cache.delete(cacheKey)
    }
    return executeInternal()
  }

  // 清除数据
  const clear = (): void => {
    data.value = null
    error.value = null
    loading.value = false
    if (cacheKey) {
      cache.delete(cacheKey)
    }
  }

  // 立即执行
  if (immediate) {
    execute()
  }

  return {
    data,
    loading,
    error,
    execute,
    refresh,
    clear,
  }
}

/**
 * 分页数据加载 composable
 */
export function usePaginatedData<T>(
  fetcher: (page: number, pageSize: number) => Promise<{ data: T[]; total: number }>,
  options: { pageSize?: number; immediate?: boolean } = {}
) {
  const { pageSize = 20, immediate = true } = options
  
  const currentPage = ref(1)
  const data = ref<T[]>([])
  const total = ref(0)
  const loading = ref(false)
  const error = ref<Error | null>(null)

  const totalPages = computed(() => Math.ceil(total.value / pageSize))
  const hasNextPage = computed(() => currentPage.value < totalPages.value)
  const hasPrevPage = computed(() => currentPage.value > 1)

  async function loadPage(page: number): Promise<void> {
    loading.value = true
    error.value = null

    try {
      const result = await fetcher(page, pageSize)
      data.value = result.data
      total.value = result.total
      currentPage.value = page
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(String(err))
    } finally {
      loading.value = false
    }
  }

  async function nextPage(): Promise<void> {
    if (hasNextPage.value) {
      await loadPage(currentPage.value + 1)
    }
  }

  async function prevPage(): Promise<void> {
    if (hasPrevPage.value) {
      await loadPage(currentPage.value - 1)
    }
  }

  async function goToPage(page: number): Promise<void> {
    if (page >= 1 && page <= totalPages.value) {
      await loadPage(page)
    }
  }

  async function refresh(): Promise<void> {
    await loadPage(currentPage.value)
  }

  // 监听页面变化
  watch(currentPage, (newPage) => {
    if (newPage >= 1 && newPage <= totalPages.value) {
      loadPage(newPage)
    }
  })

  // 立即加载第一页
  if (immediate) {
    loadPage(1)
  }

  return {
    data,
    loading,
    error,
    currentPage,
    total,
    totalPages,
    hasNextPage,
    hasPrevPage,
    nextPage,
    prevPage,
    goToPage,
    refresh,
  }
}
