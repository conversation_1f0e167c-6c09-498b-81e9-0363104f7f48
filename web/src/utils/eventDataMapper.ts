/**
 * 事件数据字段映射工具函数
 * 用于处理后端EventData存储格式变更后的字段访问
 * 提供用户友好的数据转换和展示功能
 */

// 错误类型映射
const ERROR_TYPE_MAP: Record<string, string> = {
  'script.error': 'JavaScript错误',
  'resource.error': '资源加载错误',
  'promise.error': 'Promise异常',
  'vue.error': 'Vue组件错误',
  'console.error': '控制台错误',
  'network.error': '网络请求错误',
  'syntax.error': '语法错误',
  'reference.error': '引用错误',
  'type.error': '类型错误',
  'range.error': '范围错误',
}

// 性能指标映射
const PERFORMANCE_METRICS_MAP: Record<string, string> = {
  'dns': 'DNS解析时间',
  'tcp': 'TCP连接时间',
  'ssl': 'SSL握手时间',
  'ttfb': '首字节时间',
  'dom': 'DOM解析时间',
  'load': '页面加载时间',
  'fcp': '首次内容绘制',
  'lcp': '最大内容绘制',
  'fid': '首次输入延迟',
  'cls': '累积布局偏移',
}

// 事件类型映射
const EVENT_TYPE_MAP: Record<string, string> = {
  'pv': '页面访问',
  'error': '错误事件',
  'performance': '性能监控',
  'click': '点击事件',
  'custom': '自定义事件',
  'dwell': '页面停留',
  'intersection': '元素曝光',
}

/**
 * 获取错误信息
 * 支持新旧字段名：errMessage (新) 和 message (旧)
 */
export function getErrorMessage(eventData: any): string {
  return eventData?.errMessage || eventData?.message || '未知错误'
}

/**
 * 获取用户友好的错误类型名称
 */
export function getErrorTypeName(eventId: string): string {
  return ERROR_TYPE_MAP[eventId] || eventId || '未知错误类型'
}

/**
 * 获取错误文件名
 * 目前保持 filename 字段名不变
 */
export function getErrorFilename(eventData: any): string {
  return eventData?.filename || ''
}

/**
 * 获取错误行号
 * 支持新旧字段名：line (新) 和 lineno (旧)
 */
export function getErrorLine(eventData: any): number | null {
  return eventData?.line ?? eventData?.lineno ?? null
}

/**
 * 获取错误列号
 * 支持新旧字段名：col (新) 和 colno (旧)
 */
export function getErrorCol(eventData: any): number | null {
  return eventData?.col ?? eventData?.colno ?? null
}

/**
 * 获取错误位置字符串
 * 格式：行号:列号
 */
export function getErrorLocation(eventData: any): string {
  const line = getErrorLine(eventData)
  const col = getErrorCol(eventData)
  return line !== null && col !== null ? `${line}:${col}` : ''
}

/**
 * 获取错误堆栈
 * 支持新旧字段名：errStack (新) 和 stack (旧)
 */
export function getErrorStack(eventData: any): string {
  return eventData?.errStack || eventData?.stack || ''
}

/**
 * 检查是否有错误位置信息
 */
export function hasErrorLocation(eventData: any): boolean {
  const filename = getErrorFilename(eventData)
  const line = getErrorLine(eventData)
  const col = getErrorCol(eventData)
  return !!(filename || (line !== null && col !== null))
}

/**
 * 检查是否有错误堆栈信息
 */
export function hasErrorStack(eventData: any): boolean {
  return !!getErrorStack(eventData)
}

/**
 * 获取性能数据中的持续时间
 */
export function getPerformanceDuration(eventData: any): number | null {
  return eventData?.duration ?? null
}

/**
 * 获取HTTP请求的响应状态码
 */
export function getResponseStatus(eventData: any): number | null {
  return eventData?.responseStatus ?? null
}

/**
 * 获取HTTP请求的URL
 */
export function getRequestUrl(eventData: any): string {
  return eventData?.requestUrl || eventData?.src || ''
}

/**
 * 获取HTTP请求的方法
 */
export function getRequestMethod(eventData: any): string {
  return eventData?.requestMethod || ''
}

/**
 * 获取点击事件的元素路径
 */
export function getElementPath(eventData: any): string {
  return eventData?.elementPath || ''
}

/**
 * 获取点击事件的坐标
 */
export function getClickCoordinates(eventData: any): { x: number | null; y: number | null } {
  return {
    x: eventData?.x ?? null,
    y: eventData?.y ?? null,
  }
}

/**
 * 获取页面标题
 */
export function getPageTitle(eventData: any): string {
  return eventData?.title || ''
}

/**
 * 获取事件参数
 */
export function getEventParams(eventData: any): Record<string, any> {
  return eventData?.params || {}
}

/**
 * 通用的事件数据获取函数
 * 用于获取任意字段的值，支持多个备选字段名
 */
export function getEventDataField(eventData: any, ...fieldNames: string[]): any {
  for (const fieldName of fieldNames) {
    if (eventData && eventData[fieldName] !== undefined) {
      return eventData[fieldName]
    }
  }
  return null
}

/**
 * 格式化时间戳为用户友好的格式
 */
export function formatTimestamp(timestamp: number | string, format: 'datetime' | 'time' | 'date' | 'relative' = 'datetime'): string {
  const date = new Date(typeof timestamp === 'string' ? parseInt(timestamp) : timestamp)

  if (isNaN(date.getTime())) {
    return '无效时间'
  }

  switch (format) {
    case 'datetime':
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      })
    case 'time':
      return date.toLocaleTimeString('zh-CN')
    case 'date':
      return date.toLocaleDateString('zh-CN')
    case 'relative':
      return formatRelativeTime(date)
    default:
      return date.toLocaleString('zh-CN')
  }
}

/**
 * 格式化相对时间
 */
export function formatRelativeTime(date: Date): string {
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffSeconds = Math.floor(diffMs / 1000)
  const diffMinutes = Math.floor(diffSeconds / 60)
  const diffHours = Math.floor(diffMinutes / 60)
  const diffDays = Math.floor(diffHours / 24)

  if (diffSeconds < 60) {
    return '刚刚'
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`
  } else if (diffHours < 24) {
    return `${diffHours}小时前`
  } else if (diffDays < 7) {
    return `${diffDays}天前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 格式化持续时间（毫秒）
 */
export function formatDuration(ms: number): string {
  if (ms < 1000) {
    return `${ms}ms`
  } else if (ms < 60000) {
    return `${(ms / 1000).toFixed(2)}s`
  } else {
    const minutes = Math.floor(ms / 60000)
    const seconds = ((ms % 60000) / 1000).toFixed(0)
    return `${minutes}m ${seconds}s`
  }
}

/**
 * 格式化性能指标
 */
export function formatPerformanceMetric(key: string, value: number): { label: string; value: string; unit: string } {
  const label = PERFORMANCE_METRICS_MAP[key] || key

  // 大部分性能指标都是时间（毫秒）
  if (['dns', 'tcp', 'ssl', 'ttfb', 'dom', 'load', 'fcp', 'lcp', 'fid'].includes(key)) {
    return {
      label,
      value: value.toString(),
      unit: 'ms'
    }
  }

  // CLS是比率
  if (key === 'cls') {
    return {
      label,
      value: value.toFixed(3),
      unit: ''
    }
  }

  return {
    label,
    value: value.toString(),
    unit: ''
  }
}

/**
 * 获取用户友好的事件类型名称
 */
export function getEventTypeName(eventType: string): string {
  return EVENT_TYPE_MAP[eventType] || eventType || '未知事件'
}

/**
 * 格式化事件数据用于显示
 * 返回格式化后的JSON字符串
 */
export function formatEventDataForDisplay(eventData: any): string {
  try {
    return JSON.stringify(eventData, null, 2)
  } catch (error) {
    return '无法格式化事件数据'
  }
}

/**
 * 获取错误严重程度
 */
export function getErrorSeverity(eventData: any): 'low' | 'medium' | 'high' | 'critical' {
  const errorType = eventData?.eventId || ''
  const message = getErrorMessage(eventData)

  // 根据错误类型判断严重程度
  if (errorType.includes('script') || errorType.includes('syntax')) {
    return 'critical'
  }

  if (errorType.includes('resource') || errorType.includes('network')) {
    return 'high'
  }

  if (errorType.includes('console')) {
    return 'medium'
  }

  // 根据错误信息关键词判断
  if (message.toLowerCase().includes('uncaught') || message.toLowerCase().includes('fatal')) {
    return 'critical'
  }

  return 'medium'
}

/**
 * 获取错误严重程度的显示信息
 */
export function getErrorSeverityInfo(severity: 'low' | 'medium' | 'high' | 'critical') {
  const severityMap = {
    low: { label: '低', color: 'text-green-600', bgColor: 'bg-green-100' },
    medium: { label: '中', color: 'text-yellow-600', bgColor: 'bg-yellow-100' },
    high: { label: '高', color: 'text-orange-600', bgColor: 'bg-orange-100' },
    critical: { label: '严重', color: 'text-red-600', bgColor: 'bg-red-100' },
  }

  return severityMap[severity]
}

/**
 * 格式化URL，提取有用信息
 */
export function formatUrl(url: string, maxLength: number = 50): { display: string; full: string; domain: string } {
  try {
    const urlObj = new URL(url)
    const domain = urlObj.hostname
    const pathname = urlObj.pathname
    const search = urlObj.search

    let display = pathname + search
    if (display.length > maxLength) {
      display = display.substring(0, maxLength - 3) + '...'
    }

    return {
      display: display || '/',
      full: url,
      domain,
    }
  } catch {
    return {
      display: url.length > maxLength ? url.substring(0, maxLength - 3) + '...' : url,
      full: url,
      domain: '',
    }
  }
}

/**
 * 获取设备信息的友好显示
 */
export function getDeviceInfo(eventData: any): {
  platform: string
  browser: string
  screenSize: string
  deviceType: 'desktop' | 'tablet' | 'mobile'
} {
  const platform = eventData?.platform || '未知平台'
  const vendor = eventData?.vendor || '未知浏览器'
  const screenWidth = eventData?.screenWidth || 0
  const screenHeight = eventData?.screenHeight || 0

  // 判断设备类型
  let deviceType: 'desktop' | 'tablet' | 'mobile' = 'desktop'
  if (screenWidth > 0) {
    if (screenWidth < 768) {
      deviceType = 'mobile'
    } else if (screenWidth < 1024) {
      deviceType = 'tablet'
    }
  }

  return {
    platform,
    browser: vendor,
    screenSize: screenWidth && screenHeight ? `${screenWidth}×${screenHeight}` : '未知',
    deviceType,
  }
}

/**
 * 获取性能评分
 */
export function getPerformanceScore(eventData: any): {
  score: number
  level: 'excellent' | 'good' | 'needs-improvement' | 'poor'
  label: string
} {
  const loadTime = eventData?.load || eventData?.loadon || 0
  const fcp = eventData?.fcp || 0
  const lcp = eventData?.lcp || 0

  // 简单的性能评分算法
  let score = 100

  // 页面加载时间评分
  if (loadTime > 3000) score -= 30
  else if (loadTime > 2000) score -= 20
  else if (loadTime > 1000) score -= 10

  // FCP评分
  if (fcp > 2500) score -= 20
  else if (fcp > 1800) score -= 15
  else if (fcp > 1000) score -= 10

  // LCP评分
  if (lcp > 4000) score -= 25
  else if (lcp > 2500) score -= 15
  else if (lcp > 1500) score -= 10

  score = Math.max(0, score)

  let level: 'excellent' | 'good' | 'needs-improvement' | 'poor'
  let label: string

  if (score >= 90) {
    level = 'excellent'
    label = '优秀'
  } else if (score >= 75) {
    level = 'good'
    label = '良好'
  } else if (score >= 50) {
    level = 'needs-improvement'
    label = '需要改进'
  } else {
    level = 'poor'
    label = '较差'
  }

  return { score, level, label }
}

/**
 * 获取用户行为摘要
 */
export function getUserBehaviorSummary(events: any[]): {
  totalEvents: number
  errorRate: number
  avgSessionDuration: number
  topPages: Array<{ url: string; count: number }>
  deviceTypes: Record<string, number>
} {
  const totalEvents = events.length
  const errorEvents = events.filter(e => e.event_type === 'error').length
  const errorRate = totalEvents > 0 ? (errorEvents / totalEvents) * 100 : 0

  // 计算页面访问统计
  const pageVisits: Record<string, number> = {}
  const deviceTypes: Record<string, number> = {}

  events.forEach(event => {
    // 页面统计
    if (event.trigger_page_url) {
      pageVisits[event.trigger_page_url] = (pageVisits[event.trigger_page_url] || 0) + 1
    }

    // 设备类型统计
    const deviceInfo = getDeviceInfo(event.event_data)
    deviceTypes[deviceInfo.deviceType] = (deviceTypes[deviceInfo.deviceType] || 0) + 1
  })

  const topPages = Object.entries(pageVisits)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 5)
    .map(([url, count]) => ({ url, count }))

  return {
    totalEvents,
    errorRate: parseFloat(errorRate.toFixed(2)),
    avgSessionDuration: 0, // 需要根据实际会话数据计算
    topPages,
    deviceTypes,
  }
}
