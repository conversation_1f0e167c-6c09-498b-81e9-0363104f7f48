/**
 * 性能优化工具函数
 */

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间（毫秒）
 * @param immediate 是否立即执行
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate = false
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null
      if (!immediate) func(...args)
    }
    
    const callNow = immediate && !timeout
    
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    
    if (callNow) func(...args)
  }
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param limit 限制时间（毫秒）
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 延迟执行函数
 * @param ms 延迟时间（毫秒）
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 批量处理函数
 * @param items 要处理的项目数组
 * @param processor 处理函数
 * @param batchSize 批次大小
 * @param delayMs 批次间延迟时间
 */
export async function processBatch<T, R>(
  items: T[],
  processor: (item: T, index: number) => Promise<R> | R,
  batchSize = 10,
  delayMs = 0
): Promise<R[]> {
  const results: R[] = []
  
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize)
    const batchResults = await Promise.all(
      batch.map((item, index) => processor(item, i + index))
    )
    results.push(...batchResults)
    
    // 批次间延迟，避免阻塞UI
    if (delayMs > 0 && i + batchSize < items.length) {
      await delay(delayMs)
    }
  }
  
  return results
}

/**
 * 内存使用监控
 */
export function getMemoryUsage(): {
  used: number
  total: number
  percentage: number
} | null {
  if ('memory' in performance) {
    const memory = (performance as any).memory
    return {
      used: memory.usedJSHeapSize,
      total: memory.totalJSHeapSize,
      percentage: (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100,
    }
  }
  return null
}

/**
 * 性能标记和测量
 */
export class PerformanceTracker {
  private marks: Map<string, number> = new Map()
  
  /**
   * 开始性能标记
   */
  start(name: string): void {
    this.marks.set(name, performance.now())
  }
  
  /**
   * 结束性能标记并返回耗时
   */
  end(name: string): number {
    const startTime = this.marks.get(name)
    if (!startTime) {
      console.warn(`Performance mark "${name}" not found`)
      return 0
    }
    
    const duration = performance.now() - startTime
    this.marks.delete(name)
    return duration
  }
  
  /**
   * 测量并记录性能
   */
  measure(name: string, fn: () => void | Promise<void>): Promise<number> {
    return new Promise(async (resolve) => {
      this.start(name)
      await fn()
      const duration = this.end(name)
      console.log(`Performance: ${name} took ${duration.toFixed(2)}ms`)
      resolve(duration)
    })
  }
}

/**
 * 图片懒加载
 */
export function createImageLoader(): {
  loadImage: (src: string) => Promise<HTMLImageElement>
  preloadImages: (srcs: string[]) => Promise<HTMLImageElement[]>
} {
  const imageCache = new Map<string, Promise<HTMLImageElement>>()
  
  const loadImage = (src: string): Promise<HTMLImageElement> => {
    if (imageCache.has(src)) {
      return imageCache.get(src)!
    }
    
    const promise = new Promise<HTMLImageElement>((resolve, reject) => {
      const img = new Image()
      img.onload = () => resolve(img)
      img.onerror = reject
      img.src = src
    })
    
    imageCache.set(src, promise)
    return promise
  }
  
  const preloadImages = async (srcs: string[]): Promise<HTMLImageElement[]> => {
    return Promise.all(srcs.map(loadImage))
  }
  
  return { loadImage, preloadImages }
}

/**
 * 虚拟滚动辅助函数
 */
export function calculateVirtualScrollItems(
  containerHeight: number,
  itemHeight: number,
  scrollTop: number,
  totalItems: number,
  overscan = 5
): {
  startIndex: number
  endIndex: number
  visibleItems: number
  offsetY: number
} {
  const visibleItems = Math.ceil(containerHeight / itemHeight)
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan)
  const endIndex = Math.min(totalItems - 1, startIndex + visibleItems + overscan * 2)
  const offsetY = startIndex * itemHeight
  
  return {
    startIndex,
    endIndex,
    visibleItems,
    offsetY,
  }
}

/**
 * 检查是否支持某个特性
 */
export function checkFeatureSupport(): {
  webp: boolean
  intersectionObserver: boolean
  resizeObserver: boolean
  webWorker: boolean
} {
  return {
    webp: (() => {
      const canvas = document.createElement('canvas')
      return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0
    })(),
    intersectionObserver: 'IntersectionObserver' in window,
    resizeObserver: 'ResizeObserver' in window,
    webWorker: 'Worker' in window,
  }
}

/**
 * 全局性能跟踪器实例
 */
export const performanceTracker = new PerformanceTracker()

/**
 * 全局图片加载器实例
 */
export const imageLoader = createImageLoader()
