{
    // Disable the default formatter
    "prettier.enable": false,
    "editor.formatOnSave": false,
  
    // Auto fix
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": "explicit"
    },
  
    "eslint.workingDirectories": [
      {
        "mode": "auto",
        "directory": "src"
      }
    ],
  
    // Silent the stylistic rules in you IDE, but still auto fix them
    "eslint.rules.customizations": [
      { "rule": "@stylistic", "severity": "off" },
      { "rule": "*-indent", "severity": "off" },
      { "rule": "*-spacing", "severity": "off" },
      { "rule": "*-spaces", "severity": "off" },
      { "rule": "*-order", "severity": "off" },
      { "rule": "*-dangle", "severity": "off" },
      { "rule": "*-newline", "severity": "off" },
      { "rule": "*quotes", "severity": "off" },
      { "rule": "*semi", "severity": "off" }
    ],
  
    "eslint.validate": [
      "javascript",
      "javascriptreact",
      "typescript",
      "typescriptreact",
      "vue",
      "html",
      "markdown",
      "json",
      "jsonc",
      "yaml"
    ],
  }
  